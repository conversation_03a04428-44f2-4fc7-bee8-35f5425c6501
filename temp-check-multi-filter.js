const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    const task = await prisma.task.findFirst({
      where: {
        name: '多列筛选'
      }
    });
    
    if (task) {
      console.log('多列筛选任务ID:', task.id);
      console.log('任务名称:', task.name);
      console.log('验证规则:', task.validation);
      
      // 解析验证规则
      try {
        const validationRule = JSON.parse(task.validation);
        console.log('解析后的验证规则:', JSON.stringify(validationRule, null, 2));
      } catch (e) {
        console.log('验证规则解析失败:', e.message);
      }
    } else {
      console.log('未找到多列筛选任务');
    }
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();