const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  const userEmail = '<EMAIL>'
  
  // 查找用户
  const user = await prisma.user.findUnique({
    where: { email: userEmail }
  })
  
  if (!user) {
    console.log('用户不存在，请先创建用户')
    return
  }
  
  // 查找复杂排序关卡
  const complexSortLevel = await prisma.level.findFirst({
    where: {
      name: '复杂排序',
      isMainTask: false
    }
  })
  
  if (!complexSortLevel) {
    console.log('复杂排序关卡不存在')
    return
  }
  
  // 检查是否已有进度记录
  const existingProgress = await prisma.userProgress.findUnique({
    where: {
      userId_levelId: {
        userId: user.id,
        levelId: complexSortLevel.id
      }
    }
  })
  
  if (existingProgress) {
    console.log('复杂排序关卡已有进度记录')
  } else {
    // 创建复杂排序关卡进度
    await prisma.userProgress.create({
      data: {
        userId: user.id,
        levelId: complexSortLevel.id,
        completed: true,
        completedAt: new Date(),
        score: complexSortLevel.points
      }
    })
    console.log('复杂排序关卡进度已创建')
  }
  
  // 查找简单条件格式关卡
  const simpleConditionalLevel = await prisma.level.findFirst({
    where: {
      name: '简单条件格式',
      isMainTask: false
    }
  })
  
  if (!simpleConditionalLevel) {
    console.log('简单条件格式关卡不存在')
    return
  }
  
  // 检查是否已有进度记录
  const existingConditionalProgress = await prisma.userProgress.findUnique({
    where: {
      userId_levelId: {
        userId: user.id,
        levelId: simpleConditionalLevel.id
      }
    }
  })
  
  if (existingConditionalProgress) {
    console.log('简单条件格式关卡已有进度记录')
  } else {
    // 创建简单条件格式关卡进度
    await prisma.userProgress.create({
      data: {
        userId: user.id,
        levelId: simpleConditionalLevel.id,
        completed: true,
        completedAt: new Date(),
        score: simpleConditionalLevel.points
      }
    })
    console.log('简单条件格式关卡进度已创建')
  }
  
  console.log('条件格式关卡解锁完成！')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })