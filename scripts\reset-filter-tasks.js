const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetFilterTasks() {
  try {
    console.log('开始重置筛选任务状态...');
    
    // 查找筛选任务
    const filterTasks = await prisma.task.findMany({
      where: {
        OR: [
          { name: '单列筛选' },
          { name: '多列筛选' }
        ]
      }
    });
    
    console.log(`找到 ${filterTasks.length} 个筛选任务`);
    
    // 重置任务完成状态
    for (const task of filterTasks) {
      console.log(`重置任务: ${task.name} (ID: ${task.id})`);
      
      // 删除该任务的所有完成记录
      const deletedCompletions = await prisma.taskCompletion.deleteMany({
        where: {
          taskId: task.id
        }
      });
      
      console.log(`删除了 ${deletedCompletions.count} 个完成记录`);
    }
    
    // 查找并重置关卡进度
    const filterLevel = await prisma.level.findFirst({
      where: {
        name: '数据筛选'
      }
    });
    
    if (filterLevel) {
      console.log(`重置关卡: ${filterLevel.name} (ID: ${filterLevel.id})`);
      
      // 删除该关卡的所有完成记录
      const deletedLevelCompletions = await prisma.levelCompletion.deleteMany({
        where: {
          levelId: filterLevel.id
        }
      });
      
      console.log(`删除了 ${deletedLevelCompletions.count} 个关卡完成记录`);
    }
    
    console.log('筛选任务状态重置完成！');
    console.log('现在可以重新测试筛选验证逻辑了。');
    
  } catch (error) {
    console.error('重置任务状态时出错:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行重置脚本
if (require.main === module) {
  resetFilterTasks().catch(console.error);
}

module.exports = { resetFilterTasks };
