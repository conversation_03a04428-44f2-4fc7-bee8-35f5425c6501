const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // 查找多列筛选任务
    const task = await prisma.task.findFirst({
      where: {
        name: '多列筛选'
      }
    });
    
    if (task) {
      console.log('找到多列筛选任务，当前验证规则:', task.validation);
      
      // 修正验证规则 - 根据实际数据调整期望结果
      const correctedValidation = {
        type: 'filter',
        dataRange: 'A1:D7',
        expectedVisibleRows: 2, // 期望显示2行数据（不包括表头）
        expectedFilteredData: [
          { 0: '笔记本', 1: '北京', 2: 'Q1', 3: 15000 },
          { 0: '手机', 1: '北京', 2: 'Q1', 3: 22000 }
        ]
      };
      
      // 更新任务的验证规则
      await prisma.task.update({
        where: {
          id: task.id
        },
        data: {
          validation: JSON.stringify(correctedValidation)
        }
      });
      
      console.log('多列筛选任务验证规则已更新为:', JSON.stringify(correctedValidation, null, 2));
    } else {
      console.log('未找到多列筛选任务');
    }
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();