const { test, expect } = require('@playwright/test');

test.describe('Univer性能优化测试', () => {
  test.beforeEach(async ({ page }) => {
    // 设置较长的超时时间
    test.setTimeout(60000);
    
    // 登录并导航到任务页面
    await page.goto('http://localhost:3001/auth/signin');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', '123456');
    await page.click('button[type="submit"]');
    
    // 等待登录成功并跳转
    await page.waitForURL('**/dashboard', { timeout: 10000 });
  });

  test('测试插件延迟加载功能', async ({ page }) => {
    const consoleMessages = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('基础功能插件加载完成') || 
          text.includes('高级功能插件延迟加载完成') ||
          text.includes('Univer实例已准备就绪')) {
        consoleMessages.push({ 
          timestamp: Date.now(), 
          text: text 
        });
        console.log(`[控制台] ${text}`);
      }
    });
    
    const startTime = Date.now();
    
    // 导航到任务页面
    await page.goto('http://localhost:3001/task/cmcoh96zo001tu4q805nu3eyc');
    
    // 等待基础功能插件加载完成
    await page.waitForEvent('console', msg => 
      msg.text().includes('基础功能插件加载完成'),
      { timeout: 15000 }
    );
    
    const basicLoadTime = Date.now() - startTime;
    console.log(`基础功能插件加载时间: ${basicLoadTime}ms`);
    
    // 等待高级功能插件延迟加载完成
    await page.waitForEvent('console', msg => 
      msg.text().includes('高级功能插件延迟加载完成'),
      { timeout: 10000 }
    );
    
    const totalTime = Date.now() - startTime;
    console.log(`总加载时间: ${totalTime}ms`);
    
    // 验证延迟加载确实发生了
    expect(consoleMessages.length).toBeGreaterThanOrEqual(2);
    
    // 验证基础功能先于高级功能加载
    const basicIndex = consoleMessages.findIndex(msg => 
      msg.text.includes('基础功能插件加载完成')
    );
    const advancedIndex = consoleMessages.findIndex(msg => 
      msg.text.includes('高级功能插件延迟加载完成')
    );
    
    expect(basicIndex).toBeGreaterThan(-1);
    expect(advancedIndex).toBeGreaterThan(-1);
    expect(basicIndex).toBeLessThan(advancedIndex);
    
    console.log('✅ 插件延迟加载验证通过');
  });

  // 其他测试用例已移除，专注于核心延迟加载功能测试
});