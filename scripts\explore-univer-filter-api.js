// 探索Univer API中的筛选相关方法
// 这个脚本将在浏览器控制台中运行

function exploreUniverFilterAPI() {
  console.log('=== 开始探索Univer筛选API ===');
  
  if (!window.univerAPI) {
    console.error('Univer API不可用');
    return;
  }
  
  try {
    const workbook = window.univerAPI.getActiveWorkbook();
    const worksheet = workbook.getActiveSheet();
    
    console.log('工作表对象:', worksheet);
    
    // 探索工作表对象的所有方法和属性
    const worksheetMethods = [];
    const worksheetProperties = [];
    
    for (let prop in worksheet) {
      if (typeof worksheet[prop] === 'function') {
        worksheetMethods.push(prop);
      } else {
        worksheetProperties.push(prop);
      }
    }
    
    console.log('工作表方法:', worksheetMethods);
    console.log('工作表属性:', worksheetProperties);
    
    // 查找筛选相关的方法
    const filterMethods = worksheetMethods.filter(method => 
      method.toLowerCase().includes('filter') || 
      method.toLowerCase().includes('autofilter') ||
      method.toLowerCase().includes('criteria') ||
      method.toLowerCase().includes('condition')
    );
    
    console.log('筛选相关方法:', filterMethods);
    
    // 测试一些可能的筛选API
    const filterAPIs = [
      'hasFilter',
      'getFilter',
      'getFilterRange',
      'getAutoFilter',
      'getFilterCriteria',
      'getFilterConditions',
      'getColumnFilter',
      'getFilterSettings',
      'isFilterApplied',
      'getVisibleRows',
      'getHiddenRows'
    ];
    
    const availableAPIs = {};
    
    filterAPIs.forEach(api => {
      if (typeof worksheet[api] === 'function') {
        try {
          const result = worksheet[api]();
          availableAPIs[api] = {
            exists: true,
            result: result,
            type: typeof result
          };
          console.log(`${api}():`, result);
        } catch (error) {
          availableAPIs[api] = {
            exists: true,
            error: error.message
          };
          console.log(`${api}() 调用失败:`, error.message);
        }
      } else {
        availableAPIs[api] = {
          exists: false
        };
      }
    });
    
    // 探索工作簿级别的筛选API
    console.log('\n=== 探索工作簿级别的筛选API ===');
    const workbookMethods = [];
    
    for (let prop in workbook) {
      if (typeof workbook[prop] === 'function') {
        workbookMethods.push(prop);
      }
    }
    
    const workbookFilterMethods = workbookMethods.filter(method => 
      method.toLowerCase().includes('filter')
    );
    
    console.log('工作簿筛选相关方法:', workbookFilterMethods);
    
    // 尝试获取Univer实例的更多信息
    console.log('\n=== 探索Univer实例 ===');
    console.log('Univer API对象:', window.univerAPI);
    
    // 检查是否有插件或服务相关的筛选功能
    if (window.univerAPI.getUniverInstance) {
      const univerInstance = window.univerAPI.getUniverInstance();
      console.log('Univer实例:', univerInstance);
      
      // 探索实例的方法
      const instanceMethods = [];
      for (let prop in univerInstance) {
        if (typeof univerInstance[prop] === 'function') {
          instanceMethods.push(prop);
        }
      }
      
      const instanceFilterMethods = instanceMethods.filter(method => 
        method.toLowerCase().includes('filter') ||
        method.toLowerCase().includes('sheet') ||
        method.toLowerCase().includes('data')
      );
      
      console.log('实例筛选相关方法:', instanceFilterMethods);
    }
    
    return {
      worksheetMethods,
      filterMethods,
      availableAPIs,
      workbookFilterMethods
    };
    
  } catch (error) {
    console.error('探索API时出错:', error);
    return null;
  }
}

// 探索DOM中的筛选状态
function exploreFilterDOM() {
  console.log('\n=== 探索DOM中的筛选状态 ===');
  
  // 查找所有可能的筛选相关元素
  const selectors = [
    '[class*="filter"]',
    '[data-filter]',
    '[aria-label*="filter"]',
    '.dropdown-arrow',
    '.filter-dropdown',
    '.auto-filter',
    '[class*="dropdown"]',
    '[class*="arrow"]'
  ];
  
  const foundElements = {};
  
  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
      foundElements[selector] = {
        count: elements.length,
        elements: Array.from(elements).map(el => ({
          tagName: el.tagName,
          className: el.className,
          id: el.id,
          textContent: el.textContent?.substring(0, 50),
          attributes: Array.from(el.attributes).map(attr => `${attr.name}="${attr.value}"`).join(' ')
        }))
      };
    }
  });
  
  console.log('找到的筛选相关DOM元素:', foundElements);
  
  return foundElements;
}

// 导出函数供浏览器控制台使用
window.exploreUniverFilterAPI = exploreUniverFilterAPI;
window.exploreFilterDOM = exploreFilterDOM;

console.log('筛选API探索脚本已加载。');
console.log('使用 exploreUniverFilterAPI() 探索Univer API');
console.log('使用 exploreFilterDOM() 探索DOM筛选状态');
