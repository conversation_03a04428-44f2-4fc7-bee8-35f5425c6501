const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  const userEmail = '<EMAIL>'
  
  // 查找用户
  const user = await prisma.user.findUnique({
    where: { email: userEmail }
  })
  
  if (!user) {
    console.log('用户不存在，请先创建用户')
    return
  }
  
  // 查找复杂筛选关卡
  const complexFilterLevel = await prisma.level.findFirst({
    where: {
      name: '复杂筛选',
      isMainTask: false
    }
  })
  
  if (!complexFilterLevel) {
    console.log('复杂筛选关卡不存在')
    return
  }
  
  // 检查是否已有进度记录
  const existingProgress = await prisma.userProgress.findUnique({
    where: {
      userId_levelId: {
        userId: user.id,
        levelId: complexFilterLevel.id
      }
    }
  })
  
  if (existingProgress) {
    console.log('复杂筛选关卡已有进度记录')
  } else {
    // 创建复杂筛选关卡进度
    await prisma.userProgress.create({
      data: {
        userId: user.id,
        levelId: complexFilterLevel.id,
        completed: true,
        completedAt: new Date(),
        score: complexFilterLevel.points
      }
    })
    console.log('复杂筛选关卡进度已创建')
  }
  
  // 查找简单排序关卡
  const simpleSortLevel = await prisma.level.findFirst({
    where: {
      name: '简单排序',
      isMainTask: false
    }
  })
  
  if (!simpleSortLevel) {
    console.log('简单排序关卡不存在')
    return
  }
  
  // 检查是否已有进度记录
  const existingSortProgress = await prisma.userProgress.findUnique({
    where: {
      userId_levelId: {
        userId: user.id,
        levelId: simpleSortLevel.id
      }
    }
  })
  
  if (existingSortProgress) {
    console.log('简单排序关卡已有进度记录')
  } else {
    // 创建简单排序关卡进度
    await prisma.userProgress.create({
      data: {
        userId: user.id,
        levelId: simpleSortLevel.id,
        completed: true,
        completedAt: new Date(),
        score: simpleSortLevel.points
      }
    })
    console.log('简单排序关卡进度已创建')
  }
  
  console.log('排序关卡解锁完成！')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })