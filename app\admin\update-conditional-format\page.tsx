'use client'

import { useState } from 'react'

export default function UpdateConditionalFormatPage() {
  const [isUpdating, setIsUpdating] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleUpdate = async () => {
    setIsUpdating(true)
    setResult(null)

    try {
      const response = await fetch('/api/update-conditional-format', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: '请求失败',
        error: error.message
      })
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">更新条件格式任务配置</h1>
      
      <div className="mb-6">
        <button
          onClick={handleUpdate}
          disabled={isUpdating}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
        >
          {isUpdating ? '更新中...' : '更新条件格式任务配置'}
        </button>
      </div>

      {result && (
        <div className={`p-4 rounded ${result.success ? 'bg-green-100 border border-green-400' : 'bg-red-100 border border-red-400'}`}>
          <h2 className="font-bold mb-2">更新结果：</h2>
          <pre className="whitespace-pre-wrap text-sm">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-8">
        <h2 className="text-xl font-bold mb-4">更新内容说明：</h2>
        <div className="bg-gray-100 p-4 rounded">
          <h3 className="font-bold mb-2">简单条件格式任务：</h3>
          <ul className="list-disc list-inside mb-4">
            <li>背景色改为：#f05252（第3行第3列的红色）</li>
            <li>删除："在格式下拉框中选择'浅红填充色深红色文本'"</li>
            <li>删除："建议选择明显的红色背景色以便验证"</li>
            <li>修改："在'填充'颜色选择框中选择第3行第3列的红色"</li>
          </ul>

          <h3 className="font-bold mb-2">复杂条件格式任务：</h3>
          <ul className="list-disc list-inside">
            <li>绿色：#0da471（颜色选择框中的第6列第3行）</li>
            <li>黄色：#fac815（颜色选择框中的第5列第3行）</li>
            <li>红色：#f05252（颜色选择框中的第3列第3行）</li>
            <li>明确了颜色选择框的具体位置</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
