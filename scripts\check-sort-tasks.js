// 设置环境变量
process.env.DATABASE_URL = "file:./dev.db";

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSortTasks() {
  try {
    console.log('开始查询排序任务...');

    const sortTasks = await prisma.task.findMany({
      where: {
        type: 'sort'
      },
      include: {
        level: true
      }
    });

    console.log(`找到 ${sortTasks.length} 个排序任务`);

    if (sortTasks.length === 0) {
      console.log('没有找到排序任务');
      return;
    }

    console.log('排序任务配置:');
    sortTasks.forEach((task, index) => {
      console.log(`\n=== 任务 ${index + 1} ===`);
      console.log('任务名称:', task.name);
      console.log('关卡:', task.level.name);
      console.log('验证规则:', task.validation);
      console.log('操作说明:', task.description.substring(0, 300) + '...');
    });
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkSortTasks();
