const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createUserProgress() {
  try {
    // 获取用户ID（假设用户邮箱为***************）
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!user) {
      console.log('用户不存在')
      return
    }
    
    console.log('找到用户:', user.email)
    
    // 获取所有关卡
    const levels = await prisma.level.findMany({
      orderBy: { order: 'asc' }
    })
    
    // 为前4个关卡创建完成进度，让用户可以访问数据透视表关卡
    const levelsToComplete = levels.slice(0, 4) // 前4个关卡
    
    for (const level of levelsToComplete) {
      // 检查是否已有进度记录
      const existingProgress = await prisma.userProgress.findFirst({
        where: {
          userId: user.id,
          levelId: level.id
        }
      })
      
      if (!existingProgress) {
        await prisma.userProgress.create({
          data: {
            userId: user.id,
            levelId: level.id,
            completed: true,
            score: level.points
          }
        })
        console.log(`为关卡 "${level.name}" 创建完成进度`)
      } else {
        console.log(`关卡 "${level.name}" 已有进度记录`)
      }
    }
    
    console.log('用户进度创建完成！')
    
  } catch (error) {
    console.error('创建用户进度时出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createUserProgress()