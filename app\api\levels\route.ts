import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    // 只获取主任务（父级关卡）
    const mainTasks = await prisma.level.findMany({
      where: {
        isMainTask: true
      },
      orderBy: {
        order: 'asc'
      },
      include: {
        children: {
          orderBy: {
            order: 'asc'
          },
          include: {
            tasks: {
              orderBy: {
                order: 'asc'
              }
            },
            progress: {
              where: {
                userId: session.user.id
              }
            }
          }
        },
        progress: {
          where: {
            userId: session.user.id
          }
        }
      }
    })

    return NextResponse.json(mainTasks)
  } catch (error) {
    console.error('获取关卡错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}