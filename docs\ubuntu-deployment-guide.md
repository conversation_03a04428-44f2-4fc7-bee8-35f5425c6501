# Ubuntu服务器部署指南

本指南详细介绍如何将Univer测试项目部署到Ubuntu服务器上。

## 项目概述

这是一个使用Next.js + Prisma + SQLite + NextAuth的全栈应用，包含以下主要技术栈：
- **前端框架**: Next.js 15.3.4
- **数据库**: SQLite + Prisma ORM
- **认证**: NextAuth.js
- **包管理器**: pnpm
- **UI组件**: Univer电子表格组件

## 1. 服务器环境准备

### 更新系统
```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y
```

### 安装Node.js
```bash
# 安装Node.js (推荐使用NodeSource仓库)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 安装包管理器和工具
```bash
# 安装pnpm (项目使用pnpm管理依赖)
npm install -g pnpm

# 安装PM2 (用于进程管理)
npm install -g pm2

# 安装Git
sudo apt install git -y
```

## 2. 克隆和配置项目

### 克隆项目
```bash
# 克隆项目到服务器
git clone <你的项目仓库地址> /var/www/univer_test
cd /var/www/univer_test

# 安装依赖
pnpm install
```

### 设置文件权限
```bash
# 设置适当的文件权限
sudo chown -R $USER:$USER /var/www/univer_test
chmod -R 755 /var/www/univer_test
```

## 3. 环境变量配置

### 创建生产环境配置文件
```bash
# 创建环境变量文件
nano .env.production.local
```

### 环境变量内容
```env
# 数据库配置
DATABASE_URL="file:/var/db/univer_test/prod.db"

# NextAuth配置(如用Nginx反向代理，端口好是80，可以省略)
NEXTAUTH_URL="http://你的服务器IP:3000"

NEXTAUTH_SECRET="你的随机密钥字符串"

# 生产环境
NODE_ENV="production"
```

### 生成NextAuth密钥
```bash
# 生成随机密钥
openssl rand -base64 32
```

## 4. 数据库初始化

```bash
# 生成Prisma客户端
pnpm prisma generate

# 创建数据库
pnpm prisma db push

# 运行种子数据（如果有）
pnpm prisma db seed
```

## 5. 构建项目

```bash
# 构建生产版本
pnpm build
```

## 6. 配置PM2启动

### 创建PM2配置文件
```bash
nano ecosystem.config.js
```

### PM2配置内容
```javascript
module.exports = {
  apps: [{
    name: 'univer-test',
    script: 'pnpm',  // 使用pnpm而不是npm
    args: 'start',
    cwd: '/var/www/univer_test',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

## 7. 启动应用

```bash
# 创建日志目录
mkdir -p logs

# 使用PM2启动应用
pm2 start ecosystem.config.js

# 查看应用状态
pm2 status

# 查看日志
pm2 logs univer-test

# 设置PM2开机自启
pm2 startup
pm2 save
```

## 8. 配置防火墙（可选）

```bash
# 开放3000端口
sudo ufw allow 3000

# 如果使用Nginx反向代理，开放80和443端口
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw enable
```

## 9. 配置Nginx反向代理（推荐）

### 安装Nginx
```bash
# 安装Nginx
sudo apt install nginx -y
```

### 创建站点配置
```bash
# 创建站点配置文件
sudo nano /etc/nginx/sites-available/univer-test
```

### Nginx配置内容
```nginx
server {
    listen 80;
    server_name 你的域名或IP;

    # 客户端最大请求体大小
    client_max_body_size 10M;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 静态文件缓存
    location /_next/static {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 启用站点
```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/univer-test /etc/nginx/sites-enabled/

# 删除默认站点（可选）
sudo rm /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx

# 设置开机自启
sudo systemctl enable nginx
```

## 10. SSL证书配置（可选但推荐）

### 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d 你的域名

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 11. 最终启动和验证

```bash
# 确保应用正在运行
pm2 restart univer-test

# 检查应用状态
pm2 status

# 查看应用日志
pm2 logs univer-test --lines 50

# 检查端口占用
sudo netstat -tlnp | grep :3000
```

## 常用管理命令

### PM2管理命令
```bash
# 重启应用
pm2 restart univer-test

# 停止应用
pm2 stop univer-test

# 查看实时日志
pm2 logs univer-test -f

# 监控应用
pm2 monit

# 查看详细信息
pm2 show univer-test
```

### 代码更新部署
```bash
# 更新代码后重新部署
cd /var/www/univer_test
git pull
pnpm install
pnpm build
pm2 restart univer-test
```

### 数据库管理
```bash
# 查看数据库状态
pnpm prisma studio

# 备份数据库
cp prisma/dev.db prisma/dev.db.backup.$(date +%Y%m%d_%H%M%S)

# 重置数据库（谨慎使用）
pnpm prisma db push --force-reset
```

## 访问应用

- **直接访问**: `http://你的服务器IP:3000`
- **通过Nginx**: `http://你的域名或IP`
- **HTTPS访问**: `https://你的域名`（配置SSL后）

## 监控和维护

### 日志管理
```bash
# 查看应用日志
tail -f /var/www/univer_test/logs/combined.log

# 清理旧日志
find /var/www/univer_test/logs -name "*.log" -mtime +7 -delete
```

### 系统监控
```bash
# 查看系统资源使用
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

### 定期维护任务
```bash
# 创建维护脚本
sudo nano /usr/local/bin/univer-maintenance.sh
```

```bash
#!/bin/bash
# Univer应用维护脚本

echo "开始维护任务 - $(date)"

# 清理旧日志
find /var/www/univer_test/logs -name "*.log" -mtime +7 -delete

# 备份数据库
cp /var/www/univer_test/prisma/dev.db /var/www/univer_test/backups/dev.db.$(date +%Y%m%d_%H%M%S)

# 清理旧备份（保留最近7天）
find /var/www/univer_test/backups -name "*.db.*" -mtime +7 -delete

echo "维护任务完成 - $(date)"
```

```bash
# 设置执行权限
sudo chmod +x /usr/local/bin/univer-maintenance.sh

# 添加到定时任务
sudo crontab -e
# 添加以下行（每天凌晨2点执行）：
# 0 2 * * * /usr/local/bin/univer-maintenance.sh >> /var/log/univer-maintenance.log 2>&1
```

## 故障排除

### 常见问题

1. **应用无法启动**
   ```bash
   # 检查日志
   pm2 logs univer-test
   
   # 检查端口占用
   sudo netstat -tlnp | grep :3000
   
   # 检查环境变量
   pm2 env 0
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库文件权限
   ls -la prisma/dev.db
   
   # 重新生成Prisma客户端
   pnpm prisma generate
   ```

3. **Nginx配置问题**
   ```bash
   # 测试Nginx配置
   sudo nginx -t
   
   # 查看Nginx错误日志
   sudo tail -f /var/log/nginx/error.log
   ```

### 性能优化建议

1. **启用Gzip压缩**（在Nginx配置中）
2. **配置静态文件缓存**
3. **使用CDN**（如果有静态资源）
4. **定期清理日志文件**
5. **监控系统资源使用情况**

## 安全注意事项

1. **定期更新系统和依赖包**
2. **配置防火墙规则**
3. **使用HTTPS**
4. **定期备份数据库**
5. **监控异常访问日志**
6. **不要在代码中硬编码敏感信息**

## 总结

按照以上步骤，你的Univer测试应用就可以在Ubuntu服务器上稳定运行了。记住定期进行维护和监控，确保应用的稳定性和安全性。

如果遇到问题，可以通过查看日志文件和使用提供的故障排除命令来诊断和解决问题。