const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function unlockFilterLevel() {
  try {
    // 查找用户
    const user = await prisma.user.findFirst({
      where: {
        username: 'abc'
      }
    })

    if (!user) {
      console.log('用户不存在')
      return
    }

    // 查找数据筛选关卡
    const filterLevel = await prisma.level.findFirst({
      where: {
        name: '数据筛选'
      }
    })

    if (!filterLevel) {
      console.log('数据筛选关卡不存在')
      return
    }

    // 查找数据筛选的子关卡
    const filterSubLevels = await prisma.level.findMany({
      where: {
        parentId: filterLevel.id
      }
    })

    console.log(`找到 ${filterSubLevels.length} 个数据筛选子关卡`)

    // 为第一个子关卡创建进度记录（标记为已完成，以便解锁主关卡）
    if (filterSubLevels.length > 0) {
      const firstSubLevel = filterSubLevels[0]
      const existingProgress = await prisma.userProgress.findUnique({
        where: {
          userId_levelId: {
            userId: user.id,
            levelId: firstSubLevel.id
          }
        }
      })

      if (!existingProgress) {
        await prisma.userProgress.create({
          data: {
            userId: user.id,
            levelId: firstSubLevel.id,
            completed: true,
            score: firstSubLevel.points || 125,
            attempts: 1,
            completedAt: new Date()
          }
        })
        console.log(`已完成子关卡: ${firstSubLevel.name}`)
      } else {
        // 更新为已完成状态
        await prisma.userProgress.update({
          where: {
            userId_levelId: {
              userId: user.id,
              levelId: firstSubLevel.id
            }
          },
          data: {
            completed: true,
            score: firstSubLevel.points || 125,
            attempts: Math.max(existingProgress.attempts, 1),
            completedAt: existingProgress.completedAt || new Date()
          }
        })
        console.log(`已更新子关卡为完成状态: ${firstSubLevel.name}`)
      }

      // 为第二个子关卡创建未完成的进度记录
      if (filterSubLevels.length > 1) {
        const secondSubLevel = filterSubLevels[1]
        const existingSecondProgress = await prisma.userProgress.findUnique({
          where: {
            userId_levelId: {
              userId: user.id,
              levelId: secondSubLevel.id
            }
          }
        })

        if (!existingSecondProgress) {
          await prisma.userProgress.create({
            data: {
              userId: user.id,
              levelId: secondSubLevel.id,
              completed: false,
              score: 0,
              attempts: 0
            }
          })
          console.log(`已解锁子关卡: ${secondSubLevel.name}`)
        }
      }
    }

    // 为主关卡创建进度记录
    const existingMainProgress = await prisma.userProgress.findUnique({
      where: {
        userId_levelId: {
          userId: user.id,
          levelId: filterLevel.id
        }
      }
    })

    if (!existingMainProgress) {
      await prisma.userProgress.create({
        data: {
          userId: user.id,
          levelId: filterLevel.id,
          completed: false,
          score: 0,
          attempts: 0
        }
      })
      console.log(`已解锁主关卡: ${filterLevel.name}`)
    } else {
      console.log(`主关卡已存在进度记录: ${filterLevel.name}`)
    }

    console.log('数据筛选关卡解锁完成！')

  } catch (error) {
    console.error('解锁关卡时出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

unlockFilterLevel()