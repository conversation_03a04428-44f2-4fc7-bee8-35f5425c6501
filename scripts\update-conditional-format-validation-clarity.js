// 更新条件格式任务的操作说明，明确验证条件
process.env.DATABASE_URL = "file:./dev.db"

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updateConditionalFormatValidationClarity() {
  try {
    console.log('开始更新条件格式任务的验证说明...')
    
    // 更新简单条件格式任务
    const simpleTask = await prisma.task.findFirst({
      where: {
        name: '数值条件格式'
      }
    })
    
    if (simpleTask) {
      console.log('找到简单条件格式任务，更新操作说明...')
      
      // 更新操作说明，明确验证条件
      const updatedSimpleDescription = `学习为数值设置条件格式

任务说明：
表格中已有销售数据，请为销售额大于10000的单元格设置红色背景。

操作步骤：
1. 选择数据范围C2:C6（销售额数据，不包含表头）
2. 点击"数据"菜单
3. 在"数据工具"组中点击"条件格式"
4. 选择"突出显示单元格规则" → "样式规则设置为数字" → "大于"
5. 在对话框中：
   - 输入条件值：10000
   - 在格式下拉框中选择"浅红填充色深红色文本"
   - 或点击"自定义格式"，在"填充"选项卡的颜色选择框中选择第3行第3列的红色
   - 点击"确定"

验证要求：
- C3单元格（12000）应显示红色背景
- C5单元格（15000）应显示红色背景
- 其他销售额单元格保持原样

重要说明：
- 验证的是背景色（#FF0000），不是文字色！
- 必须严格按照条件值10000设置，不能是其他值
- 必须选择红色背景，不能是其他颜色
- 必须应用到正确的范围C2:C6

💡 提示：
- 条件格式会根据数据变化自动更新
- 可以设置多个条件格式规则
- 建议选择明显的红色背景色以便验证`

      await prisma.task.update({
        where: {
          id: simpleTask.id
        },
        data: {
          description: updatedSimpleDescription
        }
      })
      
      console.log('简单条件格式任务操作说明已更新')
    } else {
      console.log('未找到简单条件格式任务')
    }
    
    console.log('条件格式验证说明更新完成！')
  } catch (error) {
    console.error('更新失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateConditionalFormatValidationClarity()
