const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function getTaskInfo() {
  try {
    // 获取所有筛选相关任务
    const filterTasks = await prisma.task.findMany({
      where: {
        OR: [
          { name: { contains: '筛选' } },
          { type: 'filter' }
        ]
      },
      include: {
        level: {
          include: {
            parent: true
          }
        }
      },
      orderBy: {
        id: 'asc'
      }
    })
    
    if (filterTasks.length > 0) {
      console.log('筛选相关任务信息:')
      // 遍历所有筛选任务并打印信息
      for (const task of filterTasks) {
        console.log(`任务ID: ${task.id}, 名称: ${task.name}, 类型: ${task.type}`);
      }
    } else {
      console.log('未找到筛选相关任务')
    }
    
  } catch (error) {
    console.error('查询任务信息时出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

getTaskInfo()