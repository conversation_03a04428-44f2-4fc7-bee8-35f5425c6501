const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixFilterValidation() {
  try {
    // 查找单列筛选任务
    const filterTask = await prisma.task.findFirst({
      where: {
        name: '单列筛选'
      }
    })
    
    if (filterTask) {
      console.log('找到单列筛选任务，当前验证规则:', filterTask.validation)
      
      // 修正验证规则
      const correctedValidation = {
        type: 'filter',
        dataRange: 'A1:C6',
        expectedVisibleRows: 2, // 应该是数字，表示2个销售部员工
        expectedFilteredData: [
          { 0: '张三', 1: '销售部', 2: 8000 },
          { 0: '王五', 1: '销售部', 2: 9000 }
        ]
      }
      
      // 更新任务的验证规则
      await prisma.task.update({
        where: {
          id: filterTask.id
        },
        data: {
          validation: JSON.stringify(correctedValidation)
        }
      })
      
      console.log('验证规则已修正为:', JSON.stringify(correctedValidation, null, 2))
    } else {
      console.log('未找到单列筛选任务')
    }
    
  } catch (error) {
    console.error('修正验证规则时出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixFilterValidation()