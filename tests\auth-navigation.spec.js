const { test, expect } = require('@playwright/test');

test.describe('Authentication and Navigation Fix', () => {
  test('should redirect to login when not authenticated', async ({ page }) => {
    // 访问主页
    await page.goto('http://localhost:3000');
    
    // 检查"开始学习"按钮存在
    const startButton = page.locator('text=开始学习');
    await expect(startButton).toBeVisible();
    
    // 点击"开始学习"按钮
    await startButton.click();
    
    // 应该跳转到登录页面
    await expect(page).toHaveURL(/\/auth\/signin/);
  });

  test('should show continue learning when authenticated', async ({ page }) => {
    // 登录
    await page.goto('http://localhost:3000/auth/signin');
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', '123456');
    await page.click('button[type="submit"]');
    
    // 等待跳转到仪表板
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    
    // 返回主页
    await page.goto('http://localhost:3000');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查按钮文本变为"继续学习"
    const continueButton = page.locator('text=继续学习');
    await expect(continueButton).toBeVisible({ timeout: 5000 });
    
    // 点击"继续学习"按钮
    await continueButton.click();
    
    // 应该跳转到仪表板而不是登录页面
    await expect(page).toHaveURL(/\/dashboard/);
  });
});