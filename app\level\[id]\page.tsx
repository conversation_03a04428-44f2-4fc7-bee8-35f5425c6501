'use client'

import { useSession } from 'next-auth/react'
import { useEffect, useState, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import Link from 'next/link'

interface Task {
  id: string
  name: string
  description: string
  type: string
  order: number
  validation: string
  initialData?: string
}

interface Level {
  id: string
  name: string
  description: string
  difficulty: number
  points: number
  parentId?: string
  tasks: Task[]
  progress: Array<{
    completed: boolean
    score: number
    attempts: number
  }>
}

interface MainTask {
  id: string
  name: string
  description: string
  difficulty: number
  points: number
  children: Level[]
}

export default function LevelPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const params = useParams()
  const levelId = params.id as string
  
  const [mainTask, setMainTask] = useState<MainTask | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  const fetchMainTask = useCallback(async () => {
    try {
      const response = await fetch('/api/levels')
      if (response.ok) {
        const mainTasks = await response.json()
        const currentMainTask = mainTasks.find((task: MainTask) => task.id === levelId)
        if (currentMainTask) {
          setMainTask(currentMainTask)
        } else {
          router.push('/dashboard')
        }
      } else {
        router.push('/dashboard')
      }
    } catch (error) {
      console.error('获取主任务失败:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [levelId, router])

  useEffect(() => {
    if (session && levelId) {
      fetchMainTask()
    }
  }, [session, levelId, fetchMainTask])

  const calculateProgress = (level: Level) => {
    if (!level.progress || level.progress.length === 0) return 0
    return level.progress[0].completed ? 100 : 0
  }

  const getTotalPoints = () => {
    if (!mainTask) return 0
    return mainTask.children.reduce((total, level) => {
      const progress = level.progress && level.progress.length > 0 ? level.progress[0] : null
      return total + (progress?.completed ? progress.score : 0)
    }, 0)
  }

  const getCompletedCount = () => {
    if (!mainTask) return 0
    return mainTask.children.filter(level => 
      level.progress && level.progress.length > 0 && level.progress[0].completed
    ).length
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session || !mainTask) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 overflow-y-auto">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="text-green-600 hover:text-green-800"
              >
                ← 返回级别列表
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                已完成: {getCompletedCount()} / {mainTask.children.length}
              </span>
              <span className="text-sm text-gray-700">
                总积分: {getTotalPoints()}
              </span>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
        {/* 主任务信息 */}
        <div className="bg-white shadow rounded-lg p-4 mb-4">
          <h2 className="text-xl font-bold text-gray-900 mb-1">{mainTask.name}</h2>
          <p className="text-sm text-gray-600 mb-3">{mainTask.description}</p>
          <div className="flex items-center space-x-6">
            <div className="flex items-center">
              <span className="text-sm text-gray-500">难度:</span>
              <div className="ml-2 flex">
                {[...Array(5)].map((_, i) => (
                  <span
                    key={i}
                    className={`text-lg ${
                      i < mainTask.difficulty ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                  >
                    ★
                  </span>
                ))}
              </div>
            </div>
            <div className="text-sm text-gray-500">
              总积分: {mainTask.points}
            </div>
            <div className="text-sm text-gray-500">
              子任务数: {mainTask.children.length}
            </div>
          </div>
        </div>

        {/* 子任务列表 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mainTask.children.map((level) => {
            const progress = calculateProgress(level)
            const isCompleted = progress === 100
            const userProgress = level.progress && level.progress.length > 0 ? level.progress[0] : null

            return (
              <div
                key={level.id}
                className={`bg-white rounded-lg shadow-md p-6 border-l-4 flex flex-col ${
                  isCompleted
                    ? 'border-green-500'
                    : 'border-blue-500'
                }`}
              >
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {level.name}
                  </h3>
                  {isCompleted && (
                    <span className="text-green-500 text-xl">✓</span>
                  )}
                </div>
                
                <p className="text-gray-600 text-sm mb-4">
                  {level.description}
                </p>
                
                <div className="space-y-2 mb-4 flex-grow">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">难度:</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <span
                          key={i}
                          className={`text-sm ${
                            i < level.difficulty ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                        >
                          ★
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">积分:</span>
                    <span className="font-medium">{level.points}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">任务数:</span>
                    <span className="font-medium">{level.tasks.length}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">得分:</span>
                    <span className="font-medium text-green-600">
                      {userProgress ? userProgress.score : '-'}
                    </span>
                  </div>
                </div>
                
                {/* 进度条 */}
                <div className="mb-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>进度</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        isCompleted ? 'bg-green-500' : 'bg-blue-500'
                      }`}
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
                
                <Link
                  href={isCompleted ? `/task/${level.tasks[0]?.id || level.id}` : `/task/${level.tasks[0]?.id || level.id}`}
                  className={`block w-full text-center py-2 px-4 rounded-md text-sm font-medium ${
                    isCompleted
                      ? 'bg-green-100 text-green-800 hover:bg-green-200'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isCompleted ? '再来一次' : '开始挑战'}
                </Link>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}