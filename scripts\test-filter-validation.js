const { spawn } = require('child_process');
const path = require('path');

async function testFilterValidation() {
  console.log('开始测试筛选验证功能...');
  
  // 启动开发服务器
  console.log('启动开发服务器...');
  const devServer = spawn('npm', ['run', 'dev'], {
    cwd: process.cwd(),
    stdio: 'pipe',
    shell: true
  });
  
  let serverReady = false;
  
  // 监听服务器输出
  devServer.stdout.on('data', (data) => {
    const output = data.toString();
    console.log('Dev Server:', output);
    
    if (output.includes('Ready') || output.includes('localhost:3000')) {
      serverReady = true;
      console.log('✓ 开发服务器已启动');
    }
  });
  
  devServer.stderr.on('data', (data) => {
    console.error('Dev Server Error:', data.toString());
  });
  
  // 等待服务器启动
  await new Promise((resolve) => {
    const checkServer = setInterval(() => {
      if (serverReady) {
        clearInterval(checkServer);
        resolve();
      }
    }, 1000);
    
    // 最多等待60秒
    setTimeout(() => {
      clearInterval(checkServer);
      resolve();
    }, 60000);
  });
  
  if (!serverReady) {
    console.error('服务器启动超时');
    devServer.kill();
    return;
  }
  
  // 等待额外时间确保服务器完全启动
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // 运行Playwright测试
  console.log('运行Playwright测试...');
  const testProcess = spawn('npx', ['playwright', 'test', 'tests/filter-validation-fixed.spec.js', '--headed'], {
    cwd: process.cwd(),
    stdio: 'inherit',
    shell: true
  });
  
  testProcess.on('close', (code) => {
    console.log(`测试进程退出，代码: ${code}`);
    
    // 关闭开发服务器
    console.log('关闭开发服务器...');
    devServer.kill();
    
    if (code === 0) {
      console.log('✓ 所有测试通过！');
    } else {
      console.log('✗ 测试失败');
    }
    
    process.exit(code);
  });
  
  testProcess.on('error', (error) => {
    console.error('测试进程错误:', error);
    devServer.kill();
    process.exit(1);
  });
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('收到中断信号，正在清理...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('收到终止信号，正在清理...');
  process.exit(0);
});

testFilterValidation().catch((error) => {
  console.error('测试脚本错误:', error);
  process.exit(1);
});
