console.log('开始筛选验证逻辑测试...\n');

// 测试单列筛选数据匹配
console.log('=== 单列筛选测试 ===');
const employeeData = [
  ['姓名', '部门', '工资'],
  ['张三', '销售部', 8000],
  ['李四', '技术部', 12000],
  ['王五', '销售部', 9000],
  ['赵六', '财务部', 7500],
  ['钱七', '人事部', 6800]
];

const expectedSingleFilter = [
  { 0: '张三', 1: '销售部', 2: 8000 },
  { 0: '王五', 1: '销售部', 2: 9000 }
];

console.log('原始数据:', employeeData);
console.log('期望筛选结果:', expectedSingleFilter);

// 模拟筛选检测逻辑
const originalDataRows = employeeData.slice(1); // 跳过表头
let matchingRows = [];

for (const expectedRow of expectedSingleFilter) {
  for (const actualRow of originalDataRows) {
    let isRowMatch = true;
    
    for (const key in expectedRow) {
      const colIndex = parseInt(key);
      const expectedValue = expectedRow[key];
      const actualValue = actualRow[colIndex];
      
      if (actualValue !== expectedValue) {
        isRowMatch = false;
        break;
      }
    }
    
    if (isRowMatch) {
      const isDuplicate = matchingRows.some(row => 
        row.every((cell, idx) => cell === actualRow[idx])
      );
      if (!isDuplicate) {
        matchingRows.push(actualRow);
      }
      break;
    }
  }
}

console.log('找到的匹配行:', matchingRows);

const canDetectSingleFilter = matchingRows.length === expectedSingleFilter.length && 
                             matchingRows.length < originalDataRows.length;

console.log('单列筛选检测结果:', canDetectSingleFilter ? '✓ 通过' : '✗ 失败');
console.log('匹配行数:', matchingRows.length, '期望行数:', expectedSingleFilter.length, '原始行数:', originalDataRows.length);

// 测试多列筛选数据匹配
console.log('\n=== 多列筛选测试 ===');
const productData = [
  ['产品', '地区', '季度', '销售额'],
  ['笔记本', '北京', 'Q1', 15000],
  ['台式机', '上海', 'Q1', 12000],
  ['笔记本', '广州', 'Q2', 18000],
  ['台式机', '北京', 'Q2', 14000],
  ['平板', '上海', 'Q1', 8000],
  ['手机', '北京', 'Q1', 22000]
];

const expectedMultiFilter = [
  { 0: '笔记本', 1: '北京', 2: 'Q1', 3: 15000 },
  { 0: '手机', 1: '北京', 2: 'Q1', 3: 22000 }
];

console.log('原始数据:', productData);
console.log('期望筛选结果:', expectedMultiFilter);

const originalProductRows = productData.slice(1); // 跳过表头
let matchingProductRows = [];

for (const expectedRow of expectedMultiFilter) {
  for (const actualRow of originalProductRows) {
    let isRowMatch = true;
    
    for (const key in expectedRow) {
      const colIndex = parseInt(key);
      const expectedValue = expectedRow[key];
      const actualValue = actualRow[colIndex];
      
      if (actualValue !== expectedValue) {
        isRowMatch = false;
        break;
      }
    }
    
    if (isRowMatch) {
      const isDuplicate = matchingProductRows.some(row => 
        row.every((cell, idx) => cell === actualRow[idx])
      );
      if (!isDuplicate) {
        matchingProductRows.push(actualRow);
      }
      break;
    }
  }
}

console.log('找到的匹配行:', matchingProductRows);

const canDetectMultiFilter = matchingProductRows.length === expectedMultiFilter.length && 
                            matchingProductRows.length < originalProductRows.length;

console.log('多列筛选检测结果:', canDetectMultiFilter ? '✓ 通过' : '✗ 失败');
console.log('匹配行数:', matchingProductRows.length, '期望行数:', expectedMultiFilter.length, '原始行数:', originalProductRows.length);

console.log('\n=== 测试总结 ===');
console.log('单列筛选:', canDetectSingleFilter ? '✓ 通过' : '✗ 失败');
console.log('多列筛选:', canDetectMultiFilter ? '✓ 通过' : '✗ 失败');

const allPassed = canDetectSingleFilter && canDetectMultiFilter;
console.log('总体结果:', allPassed ? '✓ 所有测试通过' : '✗ 存在问题需要修复');

if (allPassed) {
  console.log('\n筛选验证逻辑修复成功！');
} else {
  console.log('\n筛选验证逻辑需要进一步调整。');
}
