const { test, expect } = require('@playwright/test');

test.describe('筛选验证调试测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到筛选任务页面
    await page.goto('http://localhost:3000/task/cmcoh96zc001pu4q8lr5t91vy');
    
    // 等待页面加载完成
    await page.waitForTimeout(5000);
    
    // 等待Excel组件加载
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(2000);
  });

  test('调试：检查初始数据状态', async ({ page }) => {
    console.log('=== 检查初始数据状态 ===');
    
    // 检查Univer API是否可用
    const univerStatus = await page.evaluate(() => {
      return {
        hasUniverAPI: !!window.univerAPI,
        hasWorkbook: !!(window.univerAPI && window.univerAPI.getActiveWorkbook),
        univerInstance: typeof window.univerAPI
      };
    });
    
    console.log('Univer API状态:', univerStatus);
    
    // 获取初始数据
    const initialData = await page.evaluate(() => {
      try {
        if (!window.univerAPI) return { error: 'Univer API不可用' };
        
        const workbook = window.univerAPI.getActiveWorkbook();
        const worksheet = workbook.getActiveSheet();
        const range = worksheet.getRange('A1:C6');
        const values = range.getValues();
        
        return {
          success: true,
          data: values,
          rowCount: values ? values.length : 0
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('初始数据:', initialData);
    
    // 检查筛选相关的API方法
    const filterMethods = await page.evaluate(() => {
      try {
        if (!window.univerAPI) return { error: 'Univer API不可用' };
        
        const workbook = window.univerAPI.getActiveWorkbook();
        const worksheet = workbook.getActiveSheet();
        
        return {
          hasFilter: typeof worksheet.hasFilter,
          getVisibleRows: typeof worksheet.getVisibleRows,
          getFilterRange: typeof worksheet.getFilterRange,
          setFilter: typeof worksheet.setFilter,
          applyFilter: typeof worksheet.applyFilter
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('筛选方法可用性:', filterMethods);
  });

  test('调试：尝试应用筛选并检查状态', async ({ page }) => {
    console.log('=== 尝试应用筛选 ===');
    
    // 尝试通过JavaScript应用筛选
    const filterResult = await page.evaluate(() => {
      try {
        if (!window.univerAPI) return { error: 'Univer API不可用' };
        
        const workbook = window.univerAPI.getActiveWorkbook();
        const worksheet = workbook.getActiveSheet();
        
        // 尝试不同的筛选方法
        const methods = [];
        
        // 方法1: 直接设置筛选数据
        try {
          const filteredData = [
            ['姓名', '部门', '工资'],
            ['张三', '销售部', 8000],
            ['王五', '销售部', 9000]
          ];
          
          const range = worksheet.getRange('A1:C3');
          range.setValues(filteredData);
          methods.push('setValues: 成功');
        } catch (e) {
          methods.push('setValues: ' + e.message);
        }
        
        // 方法2: 尝试设置筛选器
        try {
          if (worksheet.setFilter) {
            const range = worksheet.getRange('A1:C6');
            worksheet.setFilter(range);
            methods.push('setFilter: 成功');
          } else {
            methods.push('setFilter: 方法不存在');
          }
        } catch (e) {
          methods.push('setFilter: ' + e.message);
        }
        
        // 方法3: 尝试应用筛选
        try {
          if (worksheet.applyFilter) {
            worksheet.applyFilter('B', ['销售部']);
            methods.push('applyFilter: 成功');
          } else {
            methods.push('applyFilter: 方法不存在');
          }
        } catch (e) {
          methods.push('applyFilter: ' + e.message);
        }
        
        return {
          success: true,
          methods: methods
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('筛选应用结果:', filterResult);
    
    // 等待一段时间让筛选生效
    await page.waitForTimeout(2000);
    
    // 检查筛选后的数据
    const afterFilterData = await page.evaluate(() => {
      try {
        if (!window.univerAPI) return { error: 'Univer API不可用' };
        
        const workbook = window.univerAPI.getActiveWorkbook();
        const worksheet = workbook.getActiveSheet();
        const range = worksheet.getRange('A1:C6');
        const values = range.getValues();
        
        // 检查筛选状态
        const filterStatus = {
          hasFilter: worksheet.hasFilter ? worksheet.hasFilter() : 'undefined',
          getVisibleRows: worksheet.getVisibleRows ? worksheet.getVisibleRows() : 'undefined',
          getFilterRange: worksheet.getFilterRange ? worksheet.getFilterRange() : 'undefined'
        };
        
        return {
          success: true,
          data: values,
          rowCount: values ? values.length : 0,
          filterStatus: filterStatus
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('筛选后数据:', afterFilterData);
  });

  test('调试：检查DOM元素和筛选状态', async ({ page }) => {
    console.log('=== 检查DOM元素 ===');
    
    // 检查筛选相关的DOM元素
    const domElements = await page.evaluate(() => {
      const filterButtons = document.querySelectorAll('.univer-filter-button, .filter-dropdown, [data-filter], .auto-filter-button, .filter-icon, [class*="filter"], .dropdown-arrow, .filter-arrow');
      const hiddenRows = document.querySelectorAll('tr[style*="display: none"], .hidden-row, [data-hidden="true"], [style*="visibility: hidden"]');
      const activeFilters = document.querySelectorAll('.filter-active, .filtered, [aria-pressed="true"], .filter-applied');
      const tableRows = document.querySelectorAll('tr');
      
      // 检查所有可能的筛选相关元素
      const allElements = document.querySelectorAll('*');
      const filterRelated = [];
      
      allElements.forEach(el => {
        const className = el.className || '';
        const id = el.id || '';
        const dataAttrs = Array.from(el.attributes).map(attr => attr.name).join(',');
        
        if (className.includes('filter') || id.includes('filter') || dataAttrs.includes('filter')) {
          filterRelated.push({
            tag: el.tagName,
            className: className,
            id: id,
            attributes: dataAttrs
          });
        }
      });
      
      return {
        filterButtons: filterButtons.length,
        hiddenRows: hiddenRows.length,
        activeFilters: activeFilters.length,
        tableRows: tableRows.length,
        filterRelated: filterRelated.slice(0, 10) // 只取前10个
      };
    });
    
    console.log('DOM元素检查结果:', domElements);
  });

  test('调试：模拟提交并查看验证过程', async ({ page }) => {
    console.log('=== 模拟提交验证 ===');
    
    // 监听控制台日志
    const logs = [];
    page.on('console', msg => {
      if (msg.text().includes('筛选验证')) {
        logs.push(msg.text());
      }
    });
    
    // 点击提交按钮
    await page.click('button:has-text("提交任务")');
    
    // 等待验证完成
    await page.waitForTimeout(3000);
    
    console.log('验证过程日志:');
    logs.forEach(log => console.log('  ', log));
    
    // 检查验证结果
    const validationResult = await page.textContent('.text-red-600, .text-green-600, [class*="error"], [class*="success"]');
    console.log('验证结果:', validationResult);
  });
});
