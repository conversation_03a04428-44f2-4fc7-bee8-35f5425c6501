'use client'

import { useSession, signOut } from 'next-auth/react'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Task {
  id: string
  name: string
  description: string
  type: string
  order: number
  validation: string
  initialData?: string
}

interface SubLevel {
  id: string
  name: string
  description: string
  difficulty: number
  order: number
  points: number
  tasks: Task[]
  progress: Array<{
    completed: boolean
    score: number
  }>
}

interface MainTask {
  id: string
  name: string
  description: string
  difficulty: number
  order: number
  points: number
  isMainTask: boolean
  children: SubLevel[]
  progress: Array<{
    completed: boolean
    score: number
  }>
}

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [mainTasks, setMainTasks] = useState<MainTask[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  useEffect(() => {
    if (session) {
      fetchLevels()
    }
  }, [session])

  const fetchLevels = async () => {
    try {
      const response = await fetch('/api/levels')
      if (response.ok) {
        const data = await response.json()
        setMainTasks(data)
      }
    } catch (error) {
      console.error('获取关卡失败:', error)
    } finally {
      setLoading(false)
    }
  }



  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  // 计算所有子任务的完成情况
  const allSubLevels = mainTasks.flatMap(mainTask => mainTask.children)
  const completedLevels = allSubLevels.filter(subLevel => subLevel.progress.length > 0).length
  const totalPoints = allSubLevels.reduce((sum, subLevel) => {
    if (subLevel.progress.length > 0) {
      return sum + subLevel.points
    }
    return sum
  }, 0)

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return 'bg-green-100 text-green-800'
      case 2:
        return 'bg-yellow-100 text-yellow-800'
      case 3:
        return 'bg-orange-100 text-orange-800'
      case 4:
        return 'bg-red-100 text-red-800'
      case 5:
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return '入门'
      case 2:
        return '简单'
      case 3:
        return '中等'
      case 4:
        return '困难'
      case 5:
        return '专家'
      default:
        return '未知'
    }
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                <Link href="/">Excel学习平台</Link>
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link 
                href="/" 
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                返回主页
              </Link>
              <span className="text-sm text-gray-700">
                欢迎，{session.user.username}
              </span>
              <span className="text-sm text-gray-500">
                积分: {session.user.score}
              </span>
              <button
                onClick={() => signOut()}
                className="text-sm text-red-600 hover:text-red-800"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="flex-1 overflow-y-auto">
        <div className="max-w-6xl mx-auto py-4 sm:px-6 lg:px-8">
        <div className="px-4 py-3 sm:px-0">
          <div className="mb-4">
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              学习进度
            </h2>
            <p className="text-sm text-gray-600">
              选择一个关卡开始你的Excel学习之旅
            </p>
          </div>

          {/* 用户进度概览 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{completedLevels}</span>
                    </div>
                  </div>
                  <div className="ml-4 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        已完成关卡
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {completedLevels} / {allSubLevels.length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{totalPoints}</span>
                    </div>
                  </div>
                  <div className="ml-4 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        总积分
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {totalPoints} 分
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <span className="text-white font-bold text-sm">%</span>
                    </div>
                  </div>
                  <div className="ml-4 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        完成进度
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {allSubLevels.length > 0 ? Math.round((completedLevels / allSubLevels.length) * 100) : 0}%
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 主任务列表 */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {mainTasks.map((mainTask) => {
              const completedSubLevels = mainTask.children.filter(subLevel => 
                subLevel.progress.length > 0 && subLevel.progress[0].completed
              ).length
              const totalSubLevels = mainTask.children.length
              const progressPercentage = totalSubLevels > 0 ? Math.round((completedSubLevels / totalSubLevels) * 100) : 0
              const isMainTaskCompleted = mainTask.progress.length > 0 && mainTask.progress[0].completed
              
              // 判断关卡状态
              const isFullyCompleted = progressPercentage === 100
              const isPartiallyCompleted = progressPercentage > 0 && progressPercentage < 100
              
              // 获取按钮样式和文字
              const getButtonConfig = () => {
                if (isFullyCompleted) {
                  return {
                    className: "w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors",
                    text: "已完成"
                  }
                } else if (isPartiallyCompleted) {
                  return {
                    className: "w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",
                    text: "继续学习"
                  }
                } else {
                  return {
                    className: "w-full inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-500 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors",
                    text: "待解锁"
                  }
                }
              }
              
              const buttonConfig = getButtonConfig()
              
              return (
                <div
                  key={mainTask.id}
                  className={`bg-white overflow-hidden shadow rounded-lg border-2 hover:shadow-lg transition-shadow ${
                    isMainTaskCompleted ? 'border-green-200' : 'border-gray-200'
                  }`}
                >
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-xl font-semibold text-gray-900">
                        {mainTask.name}
                      </h2>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        getDifficultyColor(mainTask.difficulty)
                      }`}>
                        {getDifficultyText(mainTask.difficulty)}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-4">
                      {mainTask.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">子任务进度:</span>
                        <span className="font-medium">{completedSubLevels}/{totalSubLevels}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">完成度:</span>
                        <span className="font-medium text-green-600">{progressPercentage}%</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">奖励积分:</span>
                        <span className="font-medium">{mainTask.points}分</span>
                      </div>
                    </div>
                    
                    {/* 进度条 */}
                    <div className="mb-4">
                      <div className="bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progressPercentage}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    {isMainTaskCompleted && (
                      <div className="mb-4">
                        <span className="text-sm text-green-600 font-medium">
                          ✓ 主任务已完成
                        </span>
                      </div>
                    )}
                    
                    <Link
                      href={`/level/${mainTask.id}`}
                      className={buttonConfig.className}
                    >
                      {buttonConfig.text}
                    </Link>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
        </div>
      </main>
    </div>
  )
}