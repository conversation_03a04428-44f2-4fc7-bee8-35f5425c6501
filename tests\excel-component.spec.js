const { test, expect } = require('@playwright/test');

test.describe('Excel Practice Area', () => {
  test.beforeEach(async ({ page }) => {
    // 注册测试用户
    await page.goto('http://localhost:3000/auth/signup');
    await page.fill('#email', '<EMAIL>');
    await page.fill('#username', 'testuser');
    await page.fill('#password', '123456');
    await page.fill('#confirmPassword', '123456');
    await page.click('button[type="submit"]');
    
    // 登录
    await page.goto('http://localhost:3000/auth/signin');
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', '123456');
    await page.click('button[type="submit"]');
    
    // 等待跳转到仪表板
    await page.waitForURL('**/dashboard');
  });

  test('should load Excel practice area', async ({ page }) => {
    // 点击第一个练习关卡
    await page.click('a[href^="/level/"]');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 检查页面标题
    await expect(page).toHaveTitle(/Excel练习/);
    
    // 检查是否存在Univer容器
    const univerContainer = page.locator('div[style*="width: 100%"][style*="height: 100%"]');
    await expect(univerContainer).toBeVisible();
    
    // 等待一段时间让组件初始化
    await page.waitForTimeout(3000);
    
    // 截图保存测试结果
    await page.screenshot({ path: 'test-results/excel-component-test.png' });
  });

  test('should handle component errors gracefully', async ({ page }) => {
    // 监听控制台错误
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // 访问练习页面
    await page.click('a[href^="/level/"]');
    await page.waitForLoadState('networkidle');
    
    // 等待组件加载
    await page.waitForTimeout(5000);
    
    // 检查是否有致命错误（允许一些已知的非致命错误）
    const fatalErrors = errors.filter(error => 
      !error.includes('Injector cannot be accessed after it was disposed') &&
      !error.includes('Locale not initialized') &&
      !error.includes('Fast Refresh')
    );
    
    expect(fatalErrors.length).toBe(0);
  });

  test('should display page content', async ({ page }) => {
    await page.click('a[href^="/level/"]');
    await page.waitForLoadState('networkidle');
    
    // 检查页面是否有内容显示
    const bodyText = await page.textContent('body');
    expect(bodyText.length).toBeGreaterThan(0);
    
    // 检查是否没有显示500错误页面
    const errorText = await page.textContent('body');
    expect(errorText).not.toContain('500');
    expect(errorText).not.toContain('Internal Server Error');
  });
});