import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      username: string
      score: number
      level: number
    }
  }

  interface User {
    id: string
    email: string
    username: string
    score: number
    level: number
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    username: string
    score: number
    level: number
  }
}