@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-geist-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-geist-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
} */

html,
body,
#__next {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: auto;
}

.univer-container {
  height: 100vh;
  width: 100%;
}

/* 修复Univer排序对话框位置问题 */
/* 确保排序对话框显示在页面中央而不是左上角 */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

/* 修复排序提醒对话框位置 */
[role="dialog"][aria-labelledby*="排序"] {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  max-width: 90vw !important;
  max-height: 90vh !important;
}

/* 修复自定义排序对话框位置 */
.univer-sort-dialog,
.univer-sort-panel {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  max-width: 90vw !important;
  max-height: 90vh !important;
}

/* 确保对话框背景遮罩正确显示 */
[data-radix-dialog-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 9998 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 通用对话框位置修复 */
.univer-dialog,
.univer-modal,
[class*="dialog"],
[class*="modal"] {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
}
