/**
 * 验证服务测试文件
 * 用于测试各种验证功能的正确性
 */

import { ExcelValidationService } from './validation'

// 模拟Univer API
class MockUniverAPI {
  private mockData: Record<string, unknown> = {}
  
  constructor(mockData: Record<string, unknown> = {}) {
    this.mockData = mockData
  }
  
  getActiveWorkbook() {
    return {
      getActiveSheet: () => ({
        getRange: (cell: string) => ({
          getValue: () => (this.mockData[cell] as { value?: { v: unknown } })?.value || null,
          getCellData: () => this.mockData[cell] || {},
        })
      })
    }
  }
}

// 测试用例
export async function runValidationTests() {
  console.log('开始验证服务测试...')
  
  // 测试1: 单元格值验证
  console.log('\n=== 测试1: 单元格值验证 ===')
  const mockAPI1 = new MockUniverAPI({
    'A1': { value: { v: 'Hello Excel' } }
  })
  
  const service1 = new ExcelValidationService(mockAPI1)
  const result1 = await service1.validateTask({
    type: 'cellValue',
    cell: 'A1',
    expectedValue: 'Hello Excel'
  })
  
  console.log('结果1:', result1)
  console.log('期望: success = true')
  
  // 测试2: 公式验证
  console.log('\n=== 测试2: 公式验证 ===')
  const mockAPI2 = new MockUniverAPI({
    'B3': { 
      f: '=B1+B2',
      value: { v: 30 }
    }
  })
  
  const service2 = new ExcelValidationService(mockAPI2)
  const result2 = await service2.validateTask({
    type: 'cellFormula',
    cell: 'B3',
    expectedFormula: '=B1+B2',
    expectedValue: 30
  })
  
  console.log('结果2:', result2)
  console.log('期望: success = true')
  
  // 测试3: 样式验证
  console.log('\n=== 测试3: 样式验证 ===')
  const mockAPI3 = new MockUniverAPI({
    'B1': {
      s: {
        bl: 1, // 粗体
        it: 0, // 非斜体
        cl: { rgb: '#FF0000' }, // 红色字体
      }
    }
  })
  
  const service3 = new ExcelValidationService(mockAPI3)
  const result3 = await service3.validateTask({
    type: 'cellStyle',
    cell: 'B1',
    expectedStyle: {
      bold: true,
      color: '#FF0000'
    }
  })
  
  console.log('结果3:', result3)
  console.log('期望: success = true')
  
  // 测试4: 错误情况 - 值不匹配
  console.log('\n=== 测试4: 错误情况 - 值不匹配 ===')
  const mockAPI4 = new MockUniverAPI({
    'A1': { value: { v: 'Wrong Value' } }
  })
  
  const service4 = new ExcelValidationService(mockAPI4)
  const result4 = await service4.validateTask({
    type: 'cellValue',
    cell: 'A1',
    expectedValue: 'Hello Excel'
  })
  
  console.log('结果4:', result4)
  console.log('期望: success = false')
  
  console.log('\n验证服务测试完成！')
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  runValidationTests().catch(console.error)
}