const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  const userEmail = '<EMAIL>'
  
  // 查找用户
  const user = await prisma.user.findUnique({
    where: { email: userEmail }
  })
  
  if (!user) {
    console.log('用户不存在，请先创建用户')
    return
  }
  
  // 获取所有关卡（按顺序）
  const levels = await prisma.level.findMany({
    where: { isMainTask: false }, // 只获取子关卡
    orderBy: [{ order: 'asc' }],
    include: {
      tasks: true,
      parent: true
    }
  })
  
  console.log('找到关卡数量:', levels.length)
  
  // 为前8个关卡创建完成进度（包括新增的筛选和排序关卡）
  const completedLevels = levels.slice(0, 8)
  
  for (const level of completedLevels) {
    console.log(`处理关卡: ${level.name} (${level.parent?.name})`)
    
    // 检查是否已有进度记录
    const existingProgress = await prisma.userProgress.findUnique({
      where: {
        userId_levelId: {
          userId: user.id,
          levelId: level.id
        }
      }
    })
    
    if (existingProgress) {
      console.log(`  - 关卡 ${level.name} 已有进度记录，跳过`)
      continue
    }
    
    // 创建关卡进度
    await prisma.userProgress.create({
      data: {
        userId: user.id,
        levelId: level.id,
        completed: true,
        completedAt: new Date(),
        score: level.points
      }
    })
    
    // 注意：当前schema中没有UserTaskProgress表，只跟踪关卡级别的进度
    
    console.log(`  - 关卡 ${level.name} 进度已创建`)
  }
  
  console.log('扩展用户进度创建完成！')
  console.log('用户现在可以访问条件格式关卡了')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })