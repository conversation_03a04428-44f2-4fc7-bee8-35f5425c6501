const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function fixFilterTasks() {
  try {
    console.log('开始修复筛选任务的验证规则...')
    
    // 修复简单筛选任务
    const simpleFilterTask = await prisma.task.findFirst({
      where: {
        name: '单列筛选'
      }
    })
    
    if (simpleFilterTask) {
      console.log('找到简单筛选任务，当前验证规则:', simpleFilterTask.validation)
      
      // 修正验证规则 - 使用正确的filter类型
      const correctedSimpleValidation = {
        type: 'filter',
        dataRange: 'A1:C6',
        expectedVisibleRows: 2, // 期望显示2行数据（不包括表头）
        expectedFilteredData: [
          { 0: '张三', 1: '销售部', 2: 8000 },
          { 0: '王五', 1: '销售部', 2: 9000 }
        ]
      }
      
      await prisma.task.update({
        where: {
          id: simpleFilterTask.id
        },
        data: {
          validation: JSON.stringify(correctedSimpleValidation)
        }
      })
      
      console.log('简单筛选任务验证规则已修正为:', JSON.stringify(correctedSimpleValidation, null, 2))
    } else {
      console.log('未找到简单筛选任务')
    }
    
    // 修复复杂筛选任务
    const complexFilterTask = await prisma.task.findFirst({
      where: {
        name: '多列筛选'
      }
    })
    
    if (complexFilterTask) {
      console.log('找到复杂筛选任务，当前验证规则:', complexFilterTask.validation)
      
      // 修正验证规则 - 使用正确的filter类型
      const correctedComplexValidation = {
        type: 'filter',
        dataRange: 'A1:D7',
        expectedVisibleRows: 2, // 期望显示2行数据（不包括表头）：笔记本和手机
        expectedFilteredData: [
          { 0: '笔记本', 1: '北京', 2: 'Q1', 3: 15000 },
          { 0: '手机', 1: '北京', 2: 'Q1', 3: 22000 }
        ]
      }
      
      await prisma.task.update({
        where: {
          id: complexFilterTask.id
        },
        data: {
          validation: JSON.stringify(correctedComplexValidation)
        }
      })
      
      console.log('复杂筛选任务验证规则已修正为:', JSON.stringify(correctedComplexValidation, null, 2))
    } else {
      console.log('未找到复杂筛选任务')
    }
    
    console.log('筛选任务验证规则修复完成！')
    
  } catch (error) {
    console.error('修复筛选任务验证规则时发生错误:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixFilterTasks()