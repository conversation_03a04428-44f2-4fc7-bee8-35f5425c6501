const { test, expect } = require('@playwright/test');

test.describe('筛选验证功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到应用首页
    await page.goto('http://localhost:3000');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
  });

  test('单列筛选 - 只点击筛选按钮不应通过验证', async ({ page }) => {
    // 导航到单列筛选任务
    await page.click('text=开始学习');
    await page.waitForTimeout(2000);
    
    // 查找并点击单列筛选任务
    const filterTask = page.locator('text=单列筛选').first();
    await expect(filterTask).toBeVisible();
    await filterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(3000);
    
    // 等待Univer组件完全加载
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // 截图记录初始状态
    await page.screenshot({ 
      path: 'screenshots/filter-test-initial.png',
      fullPage: true 
    });
    
    // 模拟用户只点击筛选按钮但不设置筛选条件
    // 这里我们直接提交任务，应该失败
    const submitButton = page.locator('button:has-text("提交任务")');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      await page.waitForTimeout(1000);
      
      // 应该看到验证失败的消息
      const errorMessage = page.locator('text=未检测到有效的筛选结果');
      await expect(errorMessage).toBeVisible({ timeout: 5000 });
      
      console.log('✓ 验证通过：只点击筛选按钮不设置条件时正确显示错误消息');
    }
  });

  test('单列筛选 - 正确设置筛选条件应通过验证', async ({ page }) => {
    // 导航到单列筛选任务
    await page.click('text=开始学习');
    await page.waitForTimeout(2000);
    
    // 查找并点击单列筛选任务
    const filterTask = page.locator('text=单列筛选').first();
    await expect(filterTask).toBeVisible();
    await filterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(3000);
    
    // 等待Univer组件完全加载
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // 尝试模拟正确的筛选操作
    // 注意：由于Univer是基于Canvas的，我们可能需要使用特殊的方法来交互
    
    // 首先尝试通过JavaScript直接操作Univer API
    await page.evaluate(() => {
      // 尝试获取Univer实例并应用筛选
      if (window.univerAPI) {
        try {
          const workbook = window.univerAPI.getActiveWorkbook();
          const worksheet = workbook.getActiveSheet();
          
          // 设置筛选范围
          const range = worksheet.getRange('A1:C6');
          
          // 应用筛选（这里需要根据实际的Univer API调整）
          // 注意：实际的筛选API可能不同，这里是示例
          if (worksheet.setFilter) {
            worksheet.setFilter(range);
          }
          
          console.log('筛选设置完成');
        } catch (error) {
          console.log('筛选设置失败:', error);
        }
      }
    });
    
    await page.waitForTimeout(2000);
    
    // 截图记录筛选后状态
    await page.screenshot({ 
      path: 'screenshots/filter-test-applied.png',
      fullPage: true 
    });
    
    // 提交任务
    const submitButton = page.locator('button:has-text("提交任务")');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      await page.waitForTimeout(2000);
      
      // 检查结果
      const successMessage = page.locator('text=验证通过');
      const errorMessage = page.locator('text=未检测到有效的筛选结果');
      
      if (await successMessage.isVisible()) {
        console.log('✓ 验证通过：正确设置筛选条件后验证成功');
      } else if (await errorMessage.isVisible()) {
        console.log('⚠ 注意：筛选条件可能未正确应用，需要进一步调试');
        
        // 截图记录错误状态
        await page.screenshot({ 
          path: 'screenshots/filter-test-error.png',
          fullPage: true 
        });
      }
    }
  });

  test('多列筛选 - 只点击筛选按钮不应通过验证', async ({ page }) => {
    // 导航到多列筛选任务
    await page.click('text=开始学习');
    await page.waitForTimeout(2000);
    
    // 查找并点击多列筛选任务
    const multiFilterTask = page.locator('text=多列筛选').first();
    await expect(multiFilterTask).toBeVisible();
    await multiFilterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(3000);
    
    // 等待Univer组件完全加载
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // 截图记录初始状态
    await page.screenshot({ 
      path: 'screenshots/multi-filter-test-initial.png',
      fullPage: true 
    });
    
    // 直接提交任务，应该失败
    const submitButton = page.locator('button:has-text("提交任务")');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      await page.waitForTimeout(1000);
      
      // 应该看到验证失败的消息
      const errorMessage = page.locator('text=未检测到有效的筛选结果');
      await expect(errorMessage).toBeVisible({ timeout: 5000 });
      
      console.log('✓ 验证通过：多列筛选只点击筛选按钮不设置条件时正确显示错误消息');
    }
  });

  test('验证筛选功能的DOM检测逻辑', async ({ page }) => {
    // 导航到单列筛选任务
    await page.click('text=开始学习');
    await page.waitForTimeout(2000);
    
    const filterTask = page.locator('text=单列筛选').first();
    await expect(filterTask).toBeVisible();
    await filterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(3000);
    await page.waitForSelector('canvas', { timeout: 10000 });
    await page.waitForTimeout(2000);
    
    // 检查页面上的筛选相关元素
    const filterElements = await page.evaluate(() => {
      const elements = document.querySelectorAll('[class*="filter"], [data-filter], .univer-filter-button, .filter-dropdown');
      return {
        count: elements.length,
        classes: Array.from(elements).map(el => el.className),
        hasActiveFilter: Array.from(elements).some(el => 
          el.classList.contains('active') || 
          el.classList.contains('filtered') ||
          el.getAttribute('aria-pressed') === 'true'
        )
      };
    });
    
    console.log('筛选元素检测结果:', filterElements);
    
    // 检查Univer API是否可用
    const univerAPIStatus = await page.evaluate(() => {
      return {
        hasUniverAPI: !!window.univerAPI,
        canGetWorkbook: !!(window.univerAPI && window.univerAPI.getActiveWorkbook),
        univerInstance: !!window.univerAPI
      };
    });
    
    console.log('Univer API状态:', univerAPIStatus);
    
    // 截图记录检测状态
    await page.screenshot({ 
      path: 'screenshots/filter-detection-test.png',
      fullPage: true 
    });
  });
});