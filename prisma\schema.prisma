// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  score     Int      @default(0)
  level     Int      @default(1)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 用户进度
  progress UserProgress[]
  
  @@map("users")
}

model Level {
  id          String @id @default(cuid())
  name        String
  description String
  difficulty  Int    // 1-5 难度等级
  order       Int    // 关卡顺序
  points      Int    // 完成奖励积分
  
  // 层级关系
  parentId    String? // 父级关卡ID（主任务ID）
  isMainTask  Boolean @default(false) // 是否为主任务
  
  // 关卡任务配置
  tasks       Task[]
  
  // 用户进度
  progress    UserProgress[]
  
  // 层级关系
  parent      Level?  @relation("LevelHierarchy", fields: [parentId], references: [id])
  children    Level[] @relation("LevelHierarchy")
  
  @@map("levels")
}

model Task {
  id          String @id @default(cuid())
  levelId     String
  name        String
  description String
  type        String // "formula", "format", "chart", "pivot", etc.
  order       Int    // 任务顺序
  
  // 任务验证配置 (JSON)
  validation  String // 存储验证规则的JSON字符串
  
  // 初始数据配置 (JSON)
  initialData String? // 存储初始数据的JSON字符串
  
  level       Level @relation(fields: [levelId], references: [id], onDelete: Cascade)
  
  @@map("tasks")
}

model UserProgress {
  id          String   @id @default(cuid())
  userId      String
  levelId     String
  completed   Boolean  @default(false)
  score       Int      @default(0)
  attempts    Int      @default(0)
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  user        User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  level       Level @relation(fields: [levelId], references: [id], onDelete: Cascade)
  
  @@unique([userId, levelId])
  @@map("user_progress")
}
