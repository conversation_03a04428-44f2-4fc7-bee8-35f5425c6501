// Univer TypeScript definitions

// 单元格数据类型定义
export interface CellData {
  v: unknown; // 单元格值
  s?: Record<string, unknown>; // 样式数据
  f?: string; // 公式
  t?: number; // 数据类型
  p?: Record<string, unknown>; // 其他属性
}

// 工作表数据类型
export interface WorksheetData {
  id: string;
  name: string;
  cellData: Record<number, Record<number, CellData>>;
  rowCount?: number;
  columnCount?: number;
  [key: string]: unknown;
}

// 工作簿数据类型
export interface WorkbookData {
  id: string;
  locale: string;
  name: string;
  sheetOrder: string[];
  sheets: Record<string, WorksheetData>;
  [key: string]: unknown;
}

export interface UniverInstance {
  dispose?: () => void;
  getWorkbook?: () => any;
  getActiveSheet?: () => any;
  createUnit?: (type: any, data: WorkbookData) => void;
  [key: string]: any;
}

export interface UniverAPI {
  getSheetData?: () => any;
  setSheetData?: (data: any) => void;
  getCellValue?: (row: number, col: number) => any;
  setCellValue?: (row: number, col: number, value: any) => void;
  dispose?: () => void;
  [key: string]: any;
}

export interface UniverReadyCallback {
  (instance: UniverInstance, api: UniverAPI): void;
}