const { test, expect } = require('@playwright/test');

test.describe('Excel工作区稳定性测试', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('http://localhost:3001/auth/signin');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', '123456');
    await page.click('button[type="submit"]');
    
    // 等待登录成功并跳转到dashboard
    await page.waitForURL('**/dashboard');
    await expect(page).toHaveURL(/.*dashboard/);
  });

  test('Excel工作区不应该自动刷新导致空白', async ({ page }) => {
    // 导航到指定任务页面
    await page.goto('http://localhost:3001/task/cmcmwgadb0016u4604335uii7');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 等待Excel组件加载完成
    await page.waitForSelector('.univer-container', { timeout: 10000 });
    
    // 验证Excel组件已加载
    const univerContainer = page.locator('.univer-container');
    await expect(univerContainer).toBeVisible();
    
    // 等待一段时间，确保组件完全初始化
    await page.waitForTimeout(3000);
    
    // 检查是否有任务详情显示
    await expect(page.locator('h1')).toContainText('创建透视表');
    await expect(page.locator('text=学习创建数据透视表来分析数据')).toBeVisible();
    
    // 验证初始数据是否正确显示
    const initialDataElements = [
      'A1:产品',
      'B1:地区', 
      'C1:季度',
      'D1:销售额',
      'A2:笔记本',
      'B2:北京',
      'C2:Q1',
      'D2:15000'
    ];
    
    // 等待Excel数据加载
    await page.waitForTimeout(2000);
    
    // 检查控制台是否有初始化成功的日志
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'log') {
        logs.push(msg.text());
      }
    });
    
    // 等待一段时间收集日志
    await page.waitForTimeout(1000);
    
    // 验证Univer实例已准备就绪
    const hasReadyLog = logs.some(log => log.includes('Univer实例已准备就绪'));
    expect(hasReadyLog).toBeTruthy();
    
    // 模拟用户在页面上的操作，检查是否会触发刷新
    await page.click('body'); // 点击页面
    await page.waitForTimeout(1000);
    
    // 滚动页面
    await page.mouse.wheel(0, 100);
    await page.waitForTimeout(1000);
    
    // 验证Excel组件仍然可见且没有重新加载
    await expect(univerContainer).toBeVisible();
    
    // 检查是否有重复初始化的日志（不应该有）
    const readyLogs = logs.filter(log => log.includes('Univer实例已准备就绪'));
    expect(readyLogs.length).toBeLessThanOrEqual(1); // 应该只有一次初始化
  });

  test('Excel工作区在长时间等待后仍保持稳定', async ({ page }) => {
    // 导航到任务页面
    await page.goto('http://localhost:3001/task/cmcmwgadb0016u4604335uii7');
    
    // 等待Excel组件加载
    await page.waitForSelector('.univer-container', { timeout: 10000 });
    const univerContainer = page.locator('.univer-container');
    await expect(univerContainer).toBeVisible();
    
    // 等待较长时间（模拟用户长时间停留）
    await page.waitForTimeout(5000);
    
    // 验证组件仍然可见
    await expect(univerContainer).toBeVisible();
    
    // 验证任务信息仍然显示
    await expect(page.locator('h1')).toContainText('创建透视表');
    
    // 尝试与页面交互
    await page.click('button:has-text("提交任务")');
    
    // 验证交互后组件仍然稳定
    await expect(univerContainer).toBeVisible();
  });

  test('页面刷新后Excel工作区正常加载', async ({ page }) => {
    // 导航到任务页面
    await page.goto('http://localhost:3001/task/cmcmwgadb0016u4604335uii7');
    
    // 等待Excel组件加载
    await page.waitForSelector('.univer-container', { timeout: 10000 });
    await expect(page.locator('.univer-container')).toBeVisible();
    
    // 刷新页面
    await page.reload();
    
    // 等待重新加载
    await page.waitForLoadState('networkidle');
    
    // 验证Excel组件重新加载成功
    await page.waitForSelector('.univer-container', { timeout: 10000 });
    await expect(page.locator('.univer-container')).toBeVisible();
    
    // 验证任务信息正确显示
    await expect(page.locator('h1')).toContainText('创建透视表');
  });

  test('多个任务页面间切换时Excel工作区稳定', async ({ page }) => {
    // 先访问一个任务
    await page.goto('http://localhost:3001/task/cmcmwgadb0016u4604335uii7');
    await page.waitForSelector('.univer-container', { timeout: 10000 });
    await expect(page.locator('.univer-container')).toBeVisible();
    
    // 返回dashboard
    await page.click('text=返回任务列表');
    await page.waitForURL('**/dashboard');
    
    // 再次进入同一个任务
    await page.goto('http://localhost:3001/task/cmcmwgadb0016u4604335uii7');
    await page.waitForSelector('.univer-container', { timeout: 10000 });
    await expect(page.locator('.univer-container')).toBeVisible();
    
    // 验证任务信息正确
    await expect(page.locator('h1')).toContainText('创建透视表');
  });

  test('验证Excel组件的交互功能', async ({ page }) => {
    // 导航到任务页面
    await page.goto('http://localhost:3001/task/cmcmwgadb0016u4604335uii7');
    
    // 等待Excel组件加载
    await page.waitForSelector('.univer-container', { timeout: 10000 });
    await expect(page.locator('.univer-container')).toBeVisible();
    
    // 等待组件完全初始化
    await page.waitForTimeout(3000);
    
    // 验证可以与Excel组件交互（如果有可交互的元素）
    const univerContainer = page.locator('.univer-container');
    
    // 尝试点击Excel容器
    await univerContainer.click();
    await page.waitForTimeout(1000);
    
    // 验证组件仍然稳定
    await expect(univerContainer).toBeVisible();
    
    // 验证提交按钮可用
    const submitButton = page.locator('button:has-text("提交任务")');
    await expect(submitButton).toBeVisible();
    await expect(submitButton).toBeEnabled();
  });
});