# Excel关卡验证功能指南

## 概述

本项目实现了一个完整的Excel关卡验证系统，支持多种类型的任务验证，包括单元格值、公式、格式、样式等。验证功能基于Univer Facade API实现，提供了灵活且可扩展的验证框架。

## 架构设计

### 验证规则存储

验证规则采用**数据库存储**的方式，存储在`tasks`表的`validation`字段中，格式为JSON字符串。这种设计的优势：

1. **灵活性**: 可以动态修改验证规则，无需重新部署代码
2. **可扩展性**: 易于添加新的验证类型和参数
3. **维护性**: 验证规则与业务逻辑分离，便于管理
4. **性能**: 数据库查询比文件读取更高效

### 验证服务架构

```
验证请求 → ExcelValidationService → 具体验证方法 → Univer API → 验证结果
```

- **ExcelValidationService**: 核心验证服务类
- **ValidationRule**: 验证规则接口
- **ValidationResult**: 验证结果接口

## 支持的验证类型

### 1. 单元格值验证 (cellValue)

验证指定单元格的值是否符合预期。

```json
{
  "type": "cellValue",
  "cell": "A1",
  "expectedValue": "Hello Excel"
}
```

**特性**:
- 支持字符串、数字、布尔值等类型
- 字符串比较忽略大小写和前后空格
- 数字比较支持浮点数精度处理

### 2. 公式验证 (cellFormula)

验证指定单元格的公式是否正确，可选验证计算结果。

```json
{
  "type": "cellFormula",
  "cell": "B3",
  "expectedFormula": "=B1+B2",
  "expectedValue": 30
}
```

**特性**:
- 公式比较忽略空格和大小写
- 可同时验证公式和计算结果
- 支持复杂公式验证

### 3. 格式验证 (cellFormat)

验证单元格的数字格式设置。

```json
{
  "type": "cellFormat",
  "cell": "A1",
  "expectedFormat": "currency"
}
```

**支持的格式类型**:
- `currency`: 货币格式
- `percentage`: 百分比格式
- `date`: 日期格式
- 自定义格式字符串

### 4. 样式验证 (cellStyle)

验证单元格的样式设置，如字体、颜色等。

```json
{
  "type": "cellStyle",
  "cell": "B1",
  "expectedStyle": {
    "bold": true,
    "italic": false,
    "color": "#FF0000",
    "backgroundColor": "#FFFF00"
  }
}
```

**支持的样式属性**:
- `bold`: 粗体
- `italic`: 斜体
- `color`: 字体颜色
- `backgroundColor`: 背景色

### 5. 图表验证 (chart)

验证图表的创建和配置（开发中）。

```json
{
  "type": "chart",
  "expectedType": "column",
  "dataRange": "A1:B5"
}
```

### 6. 透视表验证 (pivotTable)

验证数据透视表的创建和配置（开发中）。

```json
{
  "type": "pivotTable",
  "expectedFields": ["行字段", "列字段", "数值字段"]
}
```

## 使用方法

### 1. 在关卡页面中使用

```typescript
import { validateTask } from '@/app/lib/validation'

// 在组件中
const validateCurrentTask = async (task: Task) => {
  if (!univerAPI) {
    setMessage('Excel组件未加载完成，请稍后重试')
    return false
  }

  try {
    const validationRule = JSON.parse(task.validation)
    const result = await validateTask(univerAPI, validationRule)
    
    if (!result.success) {
      setMessage(result.message)
    }
    
    return result.success
  } catch (error) {
    console.error('验证任务失败:', error)
    setMessage('验证过程中发生错误，请检查任务配置')
    return false
  }
}
```

### 2. 直接使用验证服务

```typescript
import { ExcelValidationService } from '@/app/lib/validation'

const service = new ExcelValidationService(univerAPI)
const result = await service.validateTask({
  type: 'cellValue',
  cell: 'A1',
  expectedValue: 'Hello World'
})

if (result.success) {
  console.log('验证通过:', result.message)
} else {
  console.log('验证失败:', result.message)
  console.log('详细信息:', result.details)
}
```

## 添加新的验证类型

1. **扩展ValidationRule接口**（如需要）
2. **在ExcelValidationService中添加新的验证方法**
3. **在validateTask方法中添加新的case**
4. **更新数据库种子数据**

示例：添加单元格范围验证

```typescript
// 在ExcelValidationService中添加
private async validateCellRange(rule: ValidationRule): Promise<ValidationResult> {
  // 实现范围验证逻辑
  const workbook = this.univerAPI.getActiveWorkbook()
  const worksheet = workbook.getActiveSheet()
  const range = worksheet.getRange(rule.range)
  
  // 验证逻辑...
  
  return {
    success: true,
    message: '范围验证通过！'
  }
}

// 在validateTask方法中添加
case 'cellRange':
  return await this.validateCellRange(validationRule)
```

## 错误处理

验证服务提供了完善的错误处理机制：

1. **配置错误**: 验证规则格式不正确
2. **API错误**: Univer API调用失败
3. **数据错误**: 单元格数据获取失败
4. **类型错误**: 不支持的验证类型

所有错误都会返回详细的错误信息和调试数据。

## 性能优化

1. **缓存机制**: 可以缓存验证结果，避免重复验证
2. **批量验证**: 支持一次验证多个任务
3. **异步处理**: 所有验证操作都是异步的
4. **错误恢复**: 单个验证失败不会影响其他验证

## 测试

项目包含完整的测试套件：

```bash
# 运行验证服务测试
node -e "require('./app/lib/validation.test.ts').runValidationTests()"
```

测试覆盖：
- 单元格值验证
- 公式验证
- 样式验证
- 错误情况处理

## 最佳实践

1. **验证规则设计**:
   - 保持验证规则简单明确
   - 提供清晰的错误提示
   - 考虑用户体验

2. **性能考虑**:
   - 避免过于复杂的验证逻辑
   - 合理使用缓存
   - 异步处理长时间操作

3. **可维护性**:
   - 验证规则与业务逻辑分离
   - 使用类型安全的接口
   - 提供完整的文档和注释

4. **用户体验**:
   - 提供即时反馈
   - 清晰的成功/失败提示
   - 详细的错误说明

## 扩展计划

1. **高级验证功能**:
   - 图表验证完整实现
   - 透视表验证完整实现
   - 条件格式验证
   - 数据验证规则验证

2. **性能优化**:
   - 验证结果缓存
   - 批量验证API
   - 增量验证

3. **用户体验**:
   - 实时验证提示
   - 验证进度显示
   - 智能提示和建议

## 总结

本验证系统采用了模块化、可扩展的设计，基于数据库存储验证规则，提供了完整的验证功能。通过Univer Facade API实现了对Excel各种功能的验证支持，为Excel学习平台提供了强大的自动化验证能力。