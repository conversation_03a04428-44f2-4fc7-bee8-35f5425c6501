{"name": "univer_test", "version": "0.1.0", "private": true, "scripts": {"postinstall": "prisma generate", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "prisma": {"seed": "npx tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.11.0", "@types/bcryptjs": "^3.0.0", "@univerjs-pro/engine-chart": "^0.8.3", "@univerjs-pro/engine-pivot": "^0.8.3", "@univerjs-pro/sheets-chart": "^0.8.3", "@univerjs-pro/sheets-chart-ui": "^0.8.3", "@univerjs-pro/sheets-pivot": "^0.8.3", "@univerjs-pro/sheets-pivot-ui": "^0.8.3", "@univerjs-pro/sheets-print": "^0.8.3", "@univerjs/core": "^0.8.3", "@univerjs/design": "^0.8.3", "@univerjs/docs": "^0.8.3", "@univerjs/docs-quick-insert-ui": "^0.8.3", "@univerjs/docs-ui": "^0.8.3", "@univerjs/engine-formula": "^0.8.3", "@univerjs/engine-render": "^0.8.3", "@univerjs/presets": "^0.8.3", "@univerjs/sheets": "^0.8.3", "@univerjs/sheets-conditional-formatting": "^0.8.3", "@univerjs/sheets-conditional-formatting-ui": "^0.8.3", "@univerjs/sheets-filter": "^0.8.3", "@univerjs/sheets-filter-ui": "^0.8.3", "@univerjs/sheets-formula": "^0.8.3", "@univerjs/sheets-formula-ui": "^0.8.3", "@univerjs/sheets-numfmt": "^0.8.3", "@univerjs/sheets-numfmt-ui": "^0.8.3", "@univerjs/sheets-sort": "^0.8.3", "@univerjs/sheets-sort-ui": "^0.8.3", "@univerjs/sheets-table": "^0.8.3", "@univerjs/sheets-table-ui": "^0.8.3", "@univerjs/sheets-ui": "^0.8.3", "@univerjs/ui": "^0.8.3", "bcryptjs": "^3.0.2", "next": "15.3.4", "next-auth": "^4.24.11", "prisma": "^6.11.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@playwright/test": "^1.53.2", "@types/node": "^20.19.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}