'use client';

import dynamic from 'next/dynamic';
import { useState } from 'react';
import { UniverReadyCallback, UniverInstance, UniverAPI } from '@/types/univer';

const UniverSheet = dynamic(
  () => import('./UniverSheet'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex flex-col items-center justify-center h-96 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
        <div className="text-lg font-medium text-gray-700 mb-2">表格加载中，请稍后……</div>
        <div className="text-sm text-gray-500">正在初始化Excel组件</div>
      </div>
    )
  }
);

interface UniverWrapperProps {
  onReady?: UniverReadyCallback;
  initialData?: Record<string, unknown>;
}

export default function UniverWrapper({ onReady, initialData }: UniverWrapperProps) {
  const [isLoading, setIsLoading] = useState(true);

  const handleReady = (instance: UniverInstance, api: UniverAPI) => {
    setIsLoading(false);
    if (onReady) {
      onReady(instance, api);
    }
  };

  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-center bg-white bg-opacity-90 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
          <div className="text-base font-medium text-gray-700 mb-1">表格加载中，请稍后……</div>
          {/* <div className="text-sm text-gray-500">正在渲染Excel界面</div> */}
        </div>
      )}
      <UniverSheet onReady={handleReady} initialData={initialData} />
    </div>
  );
}