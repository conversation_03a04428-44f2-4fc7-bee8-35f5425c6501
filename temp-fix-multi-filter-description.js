const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // 查找多列筛选任务
    const task = await prisma.task.findFirst({
      where: {
        name: '多列筛选'
      }
    });
    
    if (task) {
      console.log('找到多列筛选任务，当前描述:', task.description);
      
      // 更新任务描述以匹配实际数据
      const correctedDescription = `学习对多列数据进行组合筛选

操作步骤：
1. 选择数据范围A1:D7（包含表头和所有数据）
2. 点击"数据"菜单，启用筛选功能
3. 设置地区筛选：
   - 点击B1单元格（地区列）的下拉箭头
   - 取消"全选"，只勾选"北京"
   - 点击"确定"
4. 设置季度筛选：
   - 点击C1单元格（季度列）的下拉箭头
   - 取消"全选"，只勾选"Q1"
   - 点击"确定"

验证要求：
- 筛选后应只显示2行数据（表头+2个符合条件的记录）
- 显示的产品应为：笔记本、手机
- 对应的销售额应为：15000、22000

💡 提示：
- 多列筛选是AND关系，必须同时满足所有条件
- 筛选顺序不影响结果`;
      
      // 更新任务描述
      await prisma.task.update({
        where: {
          id: task.id
        },
        data: {
          description: correctedDescription
        }
      });
      
      console.log('多列筛选任务描述已更新');
    } else {
      console.log('未找到多列筛选任务');
    }
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();