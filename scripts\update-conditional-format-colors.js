// 更新条件格式任务的颜色配置和操作说明
process.env.DATABASE_URL = "file:./dev.db"

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updateConditionalFormatColors() {
  try {
    console.log('开始更新条件格式任务的颜色配置...')
    
    // 更新简单条件格式任务
    const simpleTask = await prisma.task.findFirst({
      where: {
        name: '数值条件格式'
      }
    })
    
    if (simpleTask) {
      console.log('找到简单条件格式任务，更新配置...')
      
      // 更新操作说明，删除指定的两条，修改自定义格式描述
      const updatedSimpleDescription = `学习为数值设置条件格式

任务说明：
表格中已有销售数据，请为销售额大于10000的单元格设置红色背景。

操作步骤：
1. 选择数据范围C2:C6（销售额数据，不包含表头）
2. 点击"数据"菜单
3. 在"数据工具"组中点击"条件格式"
4. 选择"突出显示单元格规则" → "样式规则设置为数字" → "大于"
5. 在对话框中：
   - 输入条件值：10000
   - 在"填充"颜色选择框中选择第3行第3列的红色
   - 点击"确定"

验证要求：
- C3单元格（12000）应显示红色背景
- C5单元格（15000）应显示红色背景
- 其他销售额单元格保持原样

重要说明：
- 验证的是背景色（#f05252），不是文字色！
- 必须严格按照条件值10000设置，不能是其他值
- 必须选择红色背景，不能是其他颜色
- 必须应用到正确的范围C2:C6

💡 提示：
- 条件格式会根据数据变化自动更新
- 可以设置多个条件格式规则`

      // 更新验证规则，使用新的背景颜色
      const updatedSimpleValidation = {
        type: 'conditionalFormat',
        range: 'C2:C6',
        condition: 'greaterThan',
        value: 10000,
        expectedFormattedCells: ['C3', 'C5'],
        expectedBackgroundColor: '#f05252'
      }
      
      await prisma.task.update({
        where: {
          id: simpleTask.id
        },
        data: {
          description: updatedSimpleDescription,
          validation: JSON.stringify(updatedSimpleValidation)
        }
      })
      
      console.log('简单条件格式任务已更新，背景色改为 #f05252')
    } else {
      console.log('未找到简单条件格式任务')
    }
    
    // 更新复杂条件格式任务
    const complexTask = await prisma.task.findFirst({
      where: {
        name: '多条件格式'
      }
    })
    
    if (complexTask) {
      console.log('找到复杂条件格式任务，更新配置...')
      
      // 更新操作说明，修改颜色选择描述
      const updatedComplexDescription = `学习设置多个条件格式规则

任务说明：
表格中已有学生成绩数据，请设置以下条件格式：
- 成绩≥90：绿色背景（颜色选择框中的第6列第3行）
- 成绩60-89：黄色背景（颜色选择框中的第5列第3行）
- 成绩<60：红色背景（颜色选择框中的第3列第3行）

操作步骤：
1. 选择数据范围C2:C7（成绩数据）
2. 设置第一个条件（优秀）：
   - 点击"数据"菜单中的"条件格式"
   - 选择"突出显示单元格规则" → "大于或等于"
   - 输入90，在"填充"颜色选择框中选择第6列第3行的绿色
   - 点击"确定"
3. 设置第二个条件（及格）：
   - 再次点击"条件格式" → "突出显示单元格规则" → "介于"
   - 输入60到89，在"填充"颜色选择框中选择第5列第3行的黄色
   - 点击"确定"
4. 设置第三个条件（不及格）：
   - 点击"条件格式" → "突出显示单元格规则" → "小于"
   - 输入60，在"填充"颜色选择框中选择第3列第3行的红色
   - 点击"确定"

验证要求：
- 95分、92分应显示绿色背景
- 85分、78分、88分应显示黄色背景
- 45分应显示红色背景

重要说明：
- 验证的是背景色，不是文字色！
- 绿色：#0da471（第6列第3行）
- 黄色：#fac815（第5列第3行）
- 红色：#f05252（第3列第3行）

💡 提示：
- 条件格式按优先级执行，后设置的规则优先级更高
- 可以通过"管理规则"调整优先级`

      // 更新验证规则，使用新的背景颜色
      const updatedComplexValidation = {
        type: 'multiConditionalFormat',
        range: 'C2:C7',
        conditions: [
          { type: 'greaterThanOrEqual', value: 90, color: '#0da471' }, // 绿色
          { type: 'between', minValue: 60, maxValue: 89, color: '#fac815' }, // 黄色
          { type: 'lessThan', value: 60, color: '#f05252' } // 红色
        ],
        expectedResults: {
          '#0da471': ['C3', 'C4'], // 绿色：95分、92分
          '#fac815': ['C2', 'C5', 'C6'], // 黄色：85分、78分、88分
          '#f05252': ['C7'] // 红色：45分
        }
      }
      
      await prisma.task.update({
        where: {
          id: complexTask.id
        },
        data: {
          description: updatedComplexDescription,
          validation: JSON.stringify(updatedComplexValidation)
        }
      })
      
      console.log('复杂条件格式任务已更新')
      console.log('- 绿色：#0da471（第6列第3行）')
      console.log('- 黄色：#fac815（第5列第3行）')
      console.log('- 红色：#f05252（第3列第3行）')
    } else {
      console.log('未找到复杂条件格式任务')
    }
    
    console.log('条件格式颜色配置更新完成！')
  } catch (error) {
    console.error('更新失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateConditionalFormatColors()
