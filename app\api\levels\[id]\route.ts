import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/lib/auth'
import { prisma } from '@/app/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const level = await prisma.level.findUnique({
      where: {
        id: resolvedParams.id
      },
      include: {
        tasks: {
          orderBy: {
            order: 'asc'
          }
        },
        progress: {
          where: {
            userId: session.user.id
          }
        }
      }
    })

    if (!level) {
      return NextResponse.json(
        { error: '关卡不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json(level)
  } catch (error) {
    console.error('获取关卡详情错误:', error)
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    )
  }
}