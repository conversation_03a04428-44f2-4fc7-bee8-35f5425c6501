const fs = require('fs');
const path = require('path');

// 读取ICO文件并解析其结构
function parseIcoFile(buffer) {
    if (buffer.length < 6) {
        throw new Error('文件太小，不是有效的ICO文件');
    }
    
    const reserved = buffer.readUInt16LE(0);
    const type = buffer.readUInt16LE(2);
    const count = buffer.readUInt16LE(4);
    
    if (reserved !== 0 || type !== 1) {
        throw new Error('不是有效的ICO文件格式');
    }
    
    const images = [];
    let offset = 6;
    
    // 读取图像目录条目
    for (let i = 0; i < count; i++) {
        if (offset + 16 > buffer.length) {
            throw new Error('ICO文件结构损坏');
        }
        
        const width = buffer.readUInt8(offset) || 256;
        const height = buffer.readUInt8(offset + 1) || 256;
        const colorCount = buffer.readUInt8(offset + 2);
        const planes = buffer.readUInt16LE(offset + 4);
        const bitCount = buffer.readUInt16LE(offset + 6);
        const imageSize = buffer.readUInt32LE(offset + 8);
        const imageOffset = buffer.readUInt32LE(offset + 12);
        
        images.push({
            width,
            height,
            colorCount,
            planes,
            bitCount,
            imageSize,
            imageOffset,
            data: buffer.slice(imageOffset, imageOffset + imageSize)
        });
        
        offset += 16;
    }
    
    return { count, images };
}

// 简单的像素缩放函数（最近邻插值）
function resizePixelData(originalData, originalWidth, originalHeight, targetWidth, targetHeight, bytesPerPixel) {
    const resizedData = Buffer.alloc(targetWidth * targetHeight * bytesPerPixel);
    
    for (let y = 0; y < targetHeight; y++) {
        for (let x = 0; x < targetWidth; x++) {
            // 计算在原图中对应的像素位置
            const srcX = Math.floor((x * originalWidth) / targetWidth);
            const srcY = Math.floor((y * originalHeight) / targetHeight);
            
            const srcIndex = (srcY * originalWidth + srcX) * bytesPerPixel;
            const dstIndex = (y * targetWidth + x) * bytesPerPixel;
            
            // 复制像素数据
            for (let b = 0; b < bytesPerPixel; b++) {
                if (srcIndex + b < originalData.length) {
                    resizedData[dstIndex + b] = originalData[srcIndex + b];
                }
            }
        }
    }
    
    return resizedData;
}

// 处理位图数据并调整大小
function resizeBitmapData(imageData, targetSize = 16) {
    if (imageData.length < 40) {
        throw new Error('位图数据太小');
    }
    
    // 读取位图信息头
    const headerSize = imageData.readUInt32LE(0);
    const width = Math.abs(imageData.readInt32LE(4));
    const height = Math.abs(imageData.readInt32LE(8)) / 2; // ICO中高度是实际高度的2倍
    const planes = imageData.readUInt16LE(12);
    const bitCount = imageData.readUInt16LE(14);
    
    const bytesPerPixel = bitCount / 8;
    const originalPixelDataSize = width * height * bytesPerPixel;
    const originalAndMaskSize = Math.ceil((width * height) / 8);
    
    // 提取原始像素数据
    const originalPixelData = imageData.slice(40, 40 + originalPixelDataSize);
    
    // 调整像素数据大小
    const resizedPixelData = resizePixelData(originalPixelData, width, height, targetSize, targetSize, bytesPerPixel);
    
    // 创建新的位图信息头
    const newBitmapInfoHeader = Buffer.alloc(40);
    newBitmapInfoHeader.writeUInt32LE(40, 0);                    // Header size
    newBitmapInfoHeader.writeInt32LE(targetSize, 4);             // Width
    newBitmapInfoHeader.writeInt32LE(targetSize * 2, 8);         // Height (doubled for ICO)
    newBitmapInfoHeader.writeUInt16LE(planes, 12);               // Planes
    newBitmapInfoHeader.writeUInt16LE(bitCount, 14);             // Bits per pixel
    newBitmapInfoHeader.writeUInt32LE(0, 16);                    // Compression
    newBitmapInfoHeader.writeUInt32LE(0, 20);                    // Image size
    newBitmapInfoHeader.writeUInt32LE(0, 24);                    // X pixels per meter
    newBitmapInfoHeader.writeUInt32LE(0, 28);                    // Y pixels per meter
    newBitmapInfoHeader.writeUInt32LE(0, 32);                    // Colors used
    newBitmapInfoHeader.writeUInt32LE(0, 36);                    // Important colors
    
    // 创建新的AND掩码（全透明）
    const newAndMaskSize = Math.ceil((targetSize * targetSize) / 8);
    const newAndMask = Buffer.alloc(newAndMaskSize, 0);
    
    return Buffer.concat([newBitmapInfoHeader, resizedPixelData, newAndMask]);
}

// ICO文件头结构
function createIcoHeader(numImages) {
    const buffer = Buffer.alloc(6);
    buffer.writeUInt16LE(0, 0);        // Reserved
    buffer.writeUInt16LE(1, 2);        // Type (ICO)
    buffer.writeUInt16LE(numImages, 4); // Number of images
    return buffer;
}

// 图像目录条目
function createImageDirectoryEntry(width, height, colorCount, planes, bitCount, imageSize, imageOffset) {
    const buffer = Buffer.alloc(16);
    buffer.writeUInt8(width === 256 ? 0 : width, 0);
    buffer.writeUInt8(height === 256 ? 0 : height, 1);
    buffer.writeUInt8(colorCount, 2);
    buffer.writeUInt8(0, 3);                          // Reserved
    buffer.writeUInt16LE(planes, 4);
    buffer.writeUInt16LE(bitCount, 6);
    buffer.writeUInt32LE(imageSize, 8);
    buffer.writeUInt32LE(imageOffset, 12);
    return buffer;
}

// 转换单个ICO文件，保持原内容
function resizeIcoFile(inputPath, outputPath, targetSize = 16) {
    try {
        console.log(`调整 ${path.basename(inputPath)} -> ${path.basename(outputPath)}`);
        
        const inputBuffer = fs.readFileSync(inputPath);
        const { images } = parseIcoFile(inputBuffer);
        
        if (images.length === 0) {
            throw new Error('ICO文件中没有找到图像');
        }
        
        // 选择最大的图像进行缩放
        const largestImage = images.reduce((prev, current) => 
            (prev.width * prev.height > current.width * current.height) ? prev : current
        );
        
        console.log(`  原始尺寸: ${largestImage.width}x${largestImage.height}, 目标尺寸: ${targetSize}x${targetSize}`);
        
        // 调整位图数据大小
        const resizedBitmapData = resizeBitmapData(largestImage.data, targetSize);
        
        // 创建新的ICO文件
        const header = createIcoHeader(1);
        const imageOffset = 6 + 16; // Header + Directory entry
        
        const directoryEntry = createImageDirectoryEntry(
            targetSize,
            targetSize,
            0, // color count
            largestImage.planes || 1,
            largestImage.bitCount,
            resizedBitmapData.length,
            imageOffset
        );
        
        const newIcoFile = Buffer.concat([header, directoryEntry, resizedBitmapData]);
        
        fs.writeFileSync(outputPath, newIcoFile);
        console.log(`✓ 成功调整: ${path.basename(outputPath)} (${newIcoFile.length} bytes)`);
        
    } catch (error) {
        console.error(`✗ 调整失败 ${path.basename(inputPath)}:`, error.message);
    }
}

// 主函数
function main() {
    const publicDir = path.join(__dirname, 'public');
    
    if (!fs.existsSync(publicDir)) {
        console.error('public目录不存在');
        return;
    }
    
    // 获取所有原始.ico文件（排除已转换的文件）
    const files = fs.readdirSync(publicDir)
        .filter(file => file.toLowerCase().endsWith('.ico') && !file.includes('-16x16'))
        .map(file => path.join(publicDir, file));
    
    if (files.length === 0) {
        console.log('public目录中没有找到原始.ico文件');
        return;
    }
    
    console.log(`找到 ${files.length} 个原始.ico文件:`);
    files.forEach(file => console.log(`  - ${path.basename(file)}`));
    console.log('');
    
    // 调整每个文件大小
    files.forEach(inputPath => {
        const fileName = path.basename(inputPath, '.ico');
        const outputPath = path.join(publicDir, `${fileName}-resized-16x16.ico`);
        resizeIcoFile(inputPath, outputPath, 16);
    });
    
    console.log('\n调整完成！所有文件已调整为16x16像素，保持原始图标内容。');
}

// 运行脚本
main();